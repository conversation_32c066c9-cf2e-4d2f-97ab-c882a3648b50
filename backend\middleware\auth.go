package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"study-tracker-backend/models"
	"study-tracker-backend/utils"
)

// AuthMiddleware validates JWT tokens and sets user context
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Message: "Authorization header is required",
				Error:   "missing_auth_header",
			})
			c.Abort()
			return
		}

		// Check if the header starts with "Bearer "
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Message: "Invalid authorization header format",
				Error:   "invalid_auth_format",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		claims, err := utils.ValidateJWT(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Message: "Invalid or expired token",
				Error:   "invalid_token",
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireUserType middleware checks if the user has the required user type
func RequireUserType(allowedTypes ...models.UserType) gin.HandlerFunc {
	return func(c *gin.Context) {
		userType, exists := c.Get("user_type")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Message: "User type not found in context",
				Error:   "missing_user_type",
			})
			c.Abort()
			return
		}

		userTypeStr, ok := userType.(models.UserType)
		if !ok {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Message: "Invalid user type format",
				Error:   "invalid_user_type_format",
			})
			c.Abort()
			return
		}

		// Check if user type is allowed
		for _, allowedType := range allowedTypes {
			if userTypeStr == allowedType {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, models.APIResponse{
			Success: false,
			Message: "Insufficient permissions",
			Error:   "insufficient_permissions",
		})
		c.Abort()
	}
}

// RequireAdmin middleware checks if the user is an admin
func RequireAdmin() gin.HandlerFunc {
	return RequireUserType(models.UserTypeAdmin)
}

// RequireTeacherOrAdmin middleware checks if the user is a teacher or admin
func RequireTeacherOrAdmin() gin.HandlerFunc {
	return RequireUserType(models.UserTypeTeacher, models.UserTypeAdmin)
}

// OptionalAuth middleware validates JWT tokens but doesn't require them
func OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		claims, err := utils.ValidateJWT(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// Set user information in context if token is valid
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("claims", claims)

		c.Next()
	}
}

// GetUserID helper function to get user ID from context
func GetUserID(c *gin.Context) (int, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	
	id, ok := userID.(int)
	return id, ok
}

// GetUserType helper function to get user type from context
func GetUserType(c *gin.Context) (models.UserType, bool) {
	userType, exists := c.Get("user_type")
	if !exists {
		return "", false
	}
	
	uType, ok := userType.(models.UserType)
	return uType, ok
}

// GetUsername helper function to get username from context
func GetUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}
	
	uname, ok := username.(string)
	return uname, ok
}
