package main

import (
	"log"
	"os"
	"study-tracker-backend/database"
	"study-tracker-backend/handlers"
	"study-tracker-backend/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database
	if err := database.InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB()

	// Setup Gin router
	router := gin.Default()

	// CORS middleware
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{
		"http://localhost:3000",
		"http://frontend:3000",
		"http://localhost:3004", // Flutter web
		"http://127.0.0.1:3004",
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	config.AllowCredentials = true
	router.Use(cors.New(config))

	// API routes
	api := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/register", handlers.Register)
			auth.POST("/login", handlers.Login)
			auth.POST("/forgot-password", handlers.ForgotPassword)
			auth.POST("/reset-password", handlers.ResetPassword)
			auth.POST("/refresh-token", handlers.RefreshToken)
		}

		// Protected routes
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware())
		{
			// User profile
			protected.GET("/profile", handlers.GetProfile)
			protected.POST("/change-password", handlers.ChangePassword)
		}
			// Classes (Teacher/Admin only for write operations)
			protected.GET("/classes", handlers.GetClasses)
			protected.POST("/classes", middleware.RequireTeacherOrAdmin(), handlers.CreateClass)
			protected.GET("/classes/:id", handlers.GetClass)
			protected.PUT("/classes/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateClass)
			protected.DELETE("/classes/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteClass)

			// Subjects (Teacher/Admin only for write operations)
			protected.GET("/subjects", handlers.GetSubjects)
			protected.POST("/subjects", middleware.RequireTeacherOrAdmin(), handlers.CreateSubject)
			protected.GET("/subjects/:id", handlers.GetSubject)
			protected.PUT("/subjects/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateSubject)
			protected.DELETE("/subjects/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteSubject)

			// Topics (Teacher/Admin only for write operations)
			protected.GET("/topics", handlers.GetTopics)
			protected.POST("/topics", middleware.RequireTeacherOrAdmin(), handlers.CreateTopic)
			protected.GET("/topics/:id", handlers.GetTopic)
			protected.PUT("/topics/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateTopic)
			protected.DELETE("/topics/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteTopic)
			protected.PUT("/topics/reorder", middleware.RequireTeacherOrAdmin(), handlers.ReorderTopics)

			// Students (Teacher/Admin only for write operations)
			protected.GET("/students", handlers.GetStudents)
			protected.POST("/students", middleware.RequireTeacherOrAdmin(), handlers.CreateStudent)
			protected.GET("/students/:id", handlers.GetStudent)
			protected.PUT("/students/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateStudent)
			protected.DELETE("/students/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteStudent)

			// Study Records
			protected.GET("/study-records", handlers.GetStudyRecords)
			protected.POST("/study-records", handlers.CreateStudyRecord)
			protected.GET("/study-records/:id", handlers.GetStudyRecord)
			protected.PUT("/study-records/:id", handlers.UpdateStudyRecord)
			protected.DELETE("/study-records/:id", handlers.DeleteStudyRecord)

			// Analytics
			protected.GET("/analytics/student/:id", handlers.GetStudentAnalytics)
			protected.GET("/analytics/class/:id", handlers.GetClassAnalytics)
		}
	}

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
