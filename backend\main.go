package main

import (
	"log"
	"os"
	"study-tracker-backend/database"
	"study-tracker-backend/handlers"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Initialize database
	if err := database.InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB()

	// Setup Gin router
	router := gin.Default()

	// CORS middleware
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:3000", "http://frontend:3000"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	router.Use(cors.New(config))

	// API routes
	api := router.Group("/api/v1")
	{
		// Classes
		api.GET("/classes", handlers.GetClasses)
		api.POST("/classes", handlers.CreateClass)
		api.GET("/classes/:id", handlers.GetClass)
		api.PUT("/classes/:id", handlers.UpdateClass)
		api.DELETE("/classes/:id", handlers.DeleteClass)

		// Subjects
		api.GET("/subjects", handlers.GetSubjects)
		api.POST("/subjects", handlers.CreateSubject)
		api.GET("/subjects/:id", handlers.GetSubject)
		api.PUT("/subjects/:id", handlers.UpdateSubject)
		api.DELETE("/subjects/:id", handlers.DeleteSubject)

		// Topics
		api.GET("/topics", handlers.GetTopics)
		api.POST("/topics", handlers.CreateTopic)
		api.GET("/topics/:id", handlers.GetTopic)
		api.PUT("/topics/:id", handlers.UpdateTopic)
		api.DELETE("/topics/:id", handlers.DeleteTopic)
		api.PUT("/topics/reorder", handlers.ReorderTopics)

		// Students
		api.GET("/students", handlers.GetStudents)
		api.POST("/students", handlers.CreateStudent)
		api.GET("/students/:id", handlers.GetStudent)
		api.PUT("/students/:id", handlers.UpdateStudent)
		api.DELETE("/students/:id", handlers.DeleteStudent)

		// Study Records
		api.GET("/study-records", handlers.GetStudyRecords)
		api.POST("/study-records", handlers.CreateStudyRecord)
		api.GET("/study-records/:id", handlers.GetStudyRecord)
		api.PUT("/study-records/:id", handlers.UpdateStudyRecord)
		api.DELETE("/study-records/:id", handlers.DeleteStudyRecord)

		// Analytics
		api.GET("/analytics/student/:id", handlers.GetStudentAnalytics)
		api.GET("/analytics/class/:id", handlers.GetClassAnalytics)
	}

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
