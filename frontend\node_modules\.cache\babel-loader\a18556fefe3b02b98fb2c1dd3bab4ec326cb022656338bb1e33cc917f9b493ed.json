{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api/v1`,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Classes API\nexport const classesAPI = {\n  getAll: () => api.get('/classes'),\n  getById: id => api.get(`/classes/${id}`),\n  create: data => api.post('/classes', data),\n  update: (id, data) => api.put(`/classes/${id}`, data),\n  delete: id => api.delete(`/classes/${id}`)\n};\n\n// Subjects API\nexport const subjectsAPI = {\n  getAll: classId => api.get('/subjects', {\n    params: classId ? {\n      class_id: classId\n    } : {}\n  }),\n  getById: id => api.get(`/subjects/${id}`),\n  create: data => api.post('/subjects', data),\n  update: (id, data) => api.put(`/subjects/${id}`, data),\n  delete: id => api.delete(`/subjects/${id}`)\n};\nexport const topicsAPI = {\n  getAll: subjectId => api.get('/topics', {\n    params: subjectId ? {\n      subject_id: subjectId\n    } : {}\n  }),\n  getById: id => api.get(`/topics/${id}`),\n  create: data => api.post('/topics', data),\n  update: (id, data) => api.put(`/topics/${id}`, data),\n  delete: id => api.delete(`/topics/${id}`),\n  reorder: topics => api.put('/topics/reorder', {\n    topics\n  })\n};\n\n// Students API\nexport const studentsAPI = {\n  getAll: () => api.get('/students'),\n  getById: id => api.get(`/students/${id}`),\n  create: data => api.post('/students', data),\n  update: (id, data) => api.put(`/students/${id}`, data),\n  delete: id => api.delete(`/students/${id}`)\n};\n\n// Study Records API\nexport const studyRecordsAPI = {\n  getAll: () => api.get('/study-records'),\n  getById: id => api.get(`/study-records/${id}`),\n  create: data => api.post('/study-records', data),\n  update: (id, data) => api.put(`/study-records/${id}`, data),\n  delete: id => api.delete(`/study-records/${id}`)\n};\n\n// Analytics API\nexport const analyticsAPI = {\n  getStudentAnalytics: studentId => api.get(`/analytics/student/${studentId}`),\n  getClassAnalytics: classId => api.get(`/analytics/class/${classId}`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "classesAPI", "getAll", "get", "getById", "id", "data", "post", "update", "put", "delete", "subjectsAPI", "classId", "params", "class_id", "topicsAPI", "subjectId", "subject_id", "reorder", "topics", "studentsAPI", "studyRecordsAPI", "analyticsAPI", "getStudentAnalytics", "studentId", "getClassAnalytics"], "sources": ["C:/Users/<USER>/source/repository/coach ai/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api/v1`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Classes API\nexport const classesAPI = {\n  getAll: () => api.get('/classes'),\n  getById: (id) => api.get(`/classes/${id}`),\n  create: (data) => api.post('/classes', data),\n  update: (id, data) => api.put(`/classes/${id}`, data),\n  delete: (id) => api.delete(`/classes/${id}`),\n};\n\n// Subjects API\nexport const subjectsAPI = {\n  getAll: (classId) => api.get('/subjects', { params: classId ? { class_id: classId } : {} }),\n  getById: (id) => api.get(`/subjects/${id}`),\n  create: (data) => api.post('/subjects', data),\n  update: (id, data) => api.put(`/subjects/${id}`, data),\n  delete: (id) => api.delete(`/subjects/${id}`),\n};\n\nexport const topicsAPI = {\n  getAll: (subjectId) => api.get('/topics', { params: subjectId ? { subject_id: subjectId } : {} }),\n  getById: (id) => api.get(`/topics/${id}`),\n  create: (data) => api.post('/topics', data),\n  update: (id, data) => api.put(`/topics/${id}`, data),\n  delete: (id) => api.delete(`/topics/${id}`),\n  reorder: (topics) => api.put('/topics/reorder', { topics })\n};\n\n// Students API\nexport const studentsAPI = {\n  getAll: () => api.get('/students'),\n  getById: (id) => api.get(`/students/${id}`),\n  create: (data) => api.post('/students', data),\n  update: (id, data) => api.put(`/students/${id}`, data),\n  delete: (id) => api.delete(`/students/${id}`),\n};\n\n// Study Records API\nexport const studyRecordsAPI = {\n  getAll: () => api.get('/study-records'),\n  getById: (id) => api.get(`/study-records/${id}`),\n  create: (data) => api.post('/study-records', data),\n  update: (id, data) => api.put(`/study-records/${id}`, data),\n  delete: (id) => api.delete(`/study-records/${id}`),\n};\n\n// Analytics API\nexport const analyticsAPI = {\n  getStudentAnalytics: (studentId) => api.get(`/analytics/student/${studentId}`),\n  getClassAnalytics: (classId) => api.get(`/analytics/class/${classId}`),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAE,GAAGN,YAAY,SAAS;EACjCO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,UAAU,CAAC;EACjCC,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,YAAYE,EAAE,EAAE,CAAC;EAC1CP,MAAM,EAAGQ,IAAI,IAAKT,GAAG,CAACU,IAAI,CAAC,UAAU,EAAED,IAAI,CAAC;EAC5CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAKT,GAAG,CAACY,GAAG,CAAC,YAAYJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACrDI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,YAAYL,EAAE,EAAE;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMM,WAAW,GAAG;EACzBT,MAAM,EAAGU,OAAO,IAAKf,GAAG,CAACM,GAAG,CAAC,WAAW,EAAE;IAAEU,MAAM,EAAED,OAAO,GAAG;MAAEE,QAAQ,EAAEF;IAAQ,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EAC3FR,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAC3CP,MAAM,EAAGQ,IAAI,IAAKT,GAAG,CAACU,IAAI,CAAC,WAAW,EAAED,IAAI,CAAC;EAC7CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAKT,GAAG,CAACY,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACtDI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,aAAaL,EAAE,EAAE;AAC9C,CAAC;AAED,OAAO,MAAMU,SAAS,GAAG;EACvBb,MAAM,EAAGc,SAAS,IAAKnB,GAAG,CAACM,GAAG,CAAC,SAAS,EAAE;IAAEU,MAAM,EAAEG,SAAS,GAAG;MAAEC,UAAU,EAAED;IAAU,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EACjGZ,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,WAAWE,EAAE,EAAE,CAAC;EACzCP,MAAM,EAAGQ,IAAI,IAAKT,GAAG,CAACU,IAAI,CAAC,SAAS,EAAED,IAAI,CAAC;EAC3CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAKT,GAAG,CAACY,GAAG,CAAC,WAAWJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACpDI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,WAAWL,EAAE,EAAE,CAAC;EAC3Ca,OAAO,EAAGC,MAAM,IAAKtB,GAAG,CAACY,GAAG,CAAC,iBAAiB,EAAE;IAAEU;EAAO,CAAC;AAC5D,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBlB,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,WAAW,CAAC;EAClCC,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAC3CP,MAAM,EAAGQ,IAAI,IAAKT,GAAG,CAACU,IAAI,CAAC,WAAW,EAAED,IAAI,CAAC;EAC7CE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAKT,GAAG,CAACY,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EACtDI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,aAAaL,EAAE,EAAE;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMgB,eAAe,GAAG;EAC7BnB,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,gBAAgB,CAAC;EACvCC,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,kBAAkBE,EAAE,EAAE,CAAC;EAChDP,MAAM,EAAGQ,IAAI,IAAKT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAClDE,MAAM,EAAEA,CAACH,EAAE,EAAEC,IAAI,KAAKT,GAAG,CAACY,GAAG,CAAC,kBAAkBJ,EAAE,EAAE,EAAEC,IAAI,CAAC;EAC3DI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,kBAAkBL,EAAE,EAAE;AACnD,CAAC;;AAED;AACA,OAAO,MAAMiB,YAAY,GAAG;EAC1BC,mBAAmB,EAAGC,SAAS,IAAK3B,GAAG,CAACM,GAAG,CAAC,sBAAsBqB,SAAS,EAAE,CAAC;EAC9EC,iBAAiB,EAAGb,OAAO,IAAKf,GAAG,CAACM,GAAG,CAAC,oBAAoBS,OAAO,EAAE;AACvE,CAAC;AAED,eAAef,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}