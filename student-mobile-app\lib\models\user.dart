import 'schedule_entry.dart';
import 'weak_topics.dart';
import 'mock_exam.dart';

enum UserType {
  student,
  teacher,
  parent,
}

extension UserTypeExtension on UserType {
  String get displayName {
    switch (this) {
      case UserType.student:
        return '<PERSON><PERSON><PERSON><PERSON>';
      case UserType.teacher:
        return 'Öğretmen';
      case UserType.parent:
        return 'Veli';
    }
  }

  String get description {
    switch (this) {
      case UserType.student:
        return 'Çalışma planları ve AI koçluk';
      case UserType.teacher:
        return 'Öğrenci takibi ve raporlama';
      case UserType.parent:
        return 'Çocuğunuzun ilerlemesini takip edin';
    }
  }
}

enum EducationLevel {
  grade5,
  grade6,
  grade7,
  grade8,
  grade9,
  grade10,
  grade11,
  grade12,
  graduate
}

extension EducationLevelExtension on EducationLevel {
  String get displayName {
    switch (this) {
      case EducationLevel.grade5:
        return '5. Sınıf';
      case EducationLevel.grade6:
        return '6. Sınıf';
      case EducationLevel.grade7:
        return '7. Sınıf';
      case EducationLevel.grade8:
        return '8. Sınıf';
      case EducationLevel.grade9:
        return '9. Sınıf';
      case EducationLevel.grade10:
        return '10. Sınıf';
      case EducationLevel.grade11:
        return '11. Sınıf';
      case EducationLevel.grade12:
        return '12. Sınıf';
      case EducationLevel.graduate:
        return 'Mezun';
    }
  }
}

enum StudyStyle {
  visual,
  auditory,
  kinesthetic,
  readingWriting
}

extension StudyStyleExtension on StudyStyle {
  String get displayName {
    switch (this) {
      case StudyStyle.visual:
        return 'Görsel';
      case StudyStyle.auditory:
        return 'İşitsel';
      case StudyStyle.kinesthetic:
        return 'Kinestetik';
      case StudyStyle.readingWriting:
        return 'Okuma-Yazma';
    }
  }
}

class User {
  final String id;
  final String name;
  final String email;
  final DateTime birthDate;
  final UserType userType;
  final EducationLevel educationLevel;
  final String? school;
  final String? major;
  final List<String> subjects;
  final StudyStyle preferredStudyStyle;
  final int dailyStudyGoalMinutes;
  final List<String> studyDays; // ['Monday', 'Tuesday', etc.]
  final WeeklySchedule? weeklySchedule;
  final WeakTopicsProfile? weakTopicsProfile;
  final MockExamProfile? mockExamProfile;
  final String? profileImagePath;
  final DateTime createdAt;
  final bool hasCompletedOnboarding;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.birthDate,
    required this.userType,
    required this.educationLevel,
    this.school,
    this.major,
    this.subjects = const [],
    required this.preferredStudyStyle,
    required this.dailyStudyGoalMinutes,
    this.studyDays = const [],
    this.weeklySchedule,
    this.weakTopicsProfile,
    this.mockExamProfile,
    this.profileImagePath,
    required this.createdAt,
    this.hasCompletedOnboarding = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'birthDate': birthDate.toIso8601String(),
      'userType': userType.toString(),
      'educationLevel': educationLevel.toString(),
      'school': school,
      'major': major,
      'subjects': subjects,
      'preferredStudyStyle': preferredStudyStyle.toString(),
      'dailyStudyGoalMinutes': dailyStudyGoalMinutes,
      'studyDays': studyDays,
      'weeklySchedule': weeklySchedule?.toJson(),
      'weakTopicsProfile': weakTopicsProfile?.toJson(),
      'mockExamProfile': mockExamProfile?.toJson(),
      'profileImagePath': profileImagePath,
      'createdAt': createdAt.toIso8601String(),
      'hasCompletedOnboarding': hasCompletedOnboarding,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      birthDate: DateTime.parse(json['birthDate']),
      userType: UserType.values.firstWhere(
        (e) => e.toString() == json['userType'],
        orElse: () => UserType.student,
      ),
      educationLevel: EducationLevel.values.firstWhere(
        (e) => e.toString() == json['educationLevel'],
      ),
      school: json['school'],
      major: json['major'],
      subjects: List<String>.from(json['subjects'] ?? []),
      preferredStudyStyle: StudyStyle.values.firstWhere(
        (e) => e.toString() == json['preferredStudyStyle'],
      ),
      dailyStudyGoalMinutes: json['dailyStudyGoalMinutes'],
      studyDays: List<String>.from(json['studyDays'] ?? []),
      weeklySchedule: json['weeklySchedule'] != null
          ? WeeklySchedule.fromJson(json['weeklySchedule'])
          : null,
      weakTopicsProfile: json['weakTopicsProfile'] != null
          ? WeakTopicsProfile.fromJson(json['weakTopicsProfile'])
          : null,
      mockExamProfile: json['mockExamProfile'] != null
          ? MockExamProfile.fromJson(json['mockExamProfile'])
          : null,
      profileImagePath: json['profileImagePath'],
      createdAt: DateTime.parse(json['createdAt']),
      hasCompletedOnboarding: json['hasCompletedOnboarding'] ?? false,
    );
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    DateTime? birthDate,
    UserType? userType,
    EducationLevel? educationLevel,
    String? school,
    String? major,
    List<String>? subjects,
    StudyStyle? preferredStudyStyle,
    int? dailyStudyGoalMinutes,
    List<String>? studyDays,
    WeeklySchedule? weeklySchedule,
    WeakTopicsProfile? weakTopicsProfile,
    MockExamProfile? mockExamProfile,
    String? profileImagePath,
    DateTime? createdAt,
    bool? hasCompletedOnboarding,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      birthDate: birthDate ?? this.birthDate,
      userType: userType ?? this.userType,
      educationLevel: educationLevel ?? this.educationLevel,
      school: school ?? this.school,
      major: major ?? this.major,
      subjects: subjects ?? this.subjects,
      preferredStudyStyle: preferredStudyStyle ?? this.preferredStudyStyle,
      dailyStudyGoalMinutes: dailyStudyGoalMinutes ?? this.dailyStudyGoalMinutes,
      studyDays: studyDays ?? this.studyDays,
      weeklySchedule: weeklySchedule ?? this.weeklySchedule,
      weakTopicsProfile: weakTopicsProfile ?? this.weakTopicsProfile,
      mockExamProfile: mockExamProfile ?? this.mockExamProfile,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      createdAt: createdAt ?? this.createdAt,
      hasCompletedOnboarding: hasCompletedOnboarding ?? this.hasCompletedOnboarding,
    );
  }

  int get age {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  String get educationLevelText {
    switch (educationLevel) {
      case EducationLevel.grade5:
        return '5. Sınıf';
      case EducationLevel.grade6:
        return '6. Sınıf';
      case EducationLevel.grade7:
        return '7. Sınıf';
      case EducationLevel.grade8:
        return '8. Sınıf';
      case EducationLevel.grade9:
        return '9. Sınıf';
      case EducationLevel.grade10:
        return '10. Sınıf';
      case EducationLevel.grade11:
        return '11. Sınıf';
      case EducationLevel.grade12:
        return '12. Sınıf';
      case EducationLevel.graduate:
        return 'Mezun';
    }
  }

  String get studyStyleText {
    switch (preferredStudyStyle) {
      case StudyStyle.visual:
        return 'Görsel';
      case StudyStyle.auditory:
        return 'İşitsel';
      case StudyStyle.kinesthetic:
        return 'Kinestetik';
      case StudyStyle.readingWriting:
        return 'Okuma/Yazma';
    }
  }
}
