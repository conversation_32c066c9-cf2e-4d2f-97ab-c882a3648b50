import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/time_tracking_provider.dart';

class TimeTrackingScreen extends StatelessWidget {
  const TimeTrackingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Zaman Takibi'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<TimeTrackingProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPomodoroTimer(context, provider),
                const SizedBox(height: 24),
                _buildTodayStats(provider),
                const SizedBox(height: 24),
                _buildRecentEntries(provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPomodoroTimer(BuildContext context, TimeTrackingProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const Text(
              'Pomodoro Timer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 4,
                ),
              ),
              child: Center(
                child: Text(
                  provider.formattedTime,
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (provider.currentSubject != null)
              Text(
                'Çalışılan Konu: ${provider.currentSubject}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (!provider.isRunning && !provider.isPaused)
                  ElevatedButton.icon(
                    onPressed: () => _showStartDialog(context, provider),
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Başlat'),
                  ),
                if (provider.isRunning)
                  ElevatedButton.icon(
                    onPressed: provider.pauseTimer,
                    icon: const Icon(Icons.pause),
                    label: const Text('Duraklat'),
                  ),
                if (provider.isPaused)
                  ElevatedButton.icon(
                    onPressed: provider.resumeTimer,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Devam Et'),
                  ),
                if (provider.isRunning || provider.isPaused)
                  ElevatedButton.icon(
                    onPressed: provider.stopTimer,
                    icon: const Icon(Icons.stop),
                    label: const Text('Durdur'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Tamamlanan Pomodoro: ${provider.pomodoroCount}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayStats(TimeTrackingProvider provider) {
    final todayMinutes = provider.getTodayTotalMinutes();
    final todayEntries = provider.getTodayEntries();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Bugünün Özeti',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Toplam Süre',
                    '${todayMinutes}dk',
                    Icons.timer,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Oturum Sayısı',
                    '${todayEntries.length}',
                    Icons.event,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentEntries(TimeTrackingProvider provider) {
    final recentEntries = provider.getTodayEntries().take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Son Çalışmalar',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (recentEntries.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'Henüz çalışma kaydı yok',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ),
              )
            else
              ...recentEntries.map((entry) => ListTile(
                leading: CircleAvatar(
                  backgroundColor: entry.isPomodoro ? Colors.red : Colors.blue,
                  child: Icon(
                    entry.isPomodoro ? Icons.timer : Icons.play_arrow,
                    color: Colors.white,
                  ),
                ),
                title: Text(entry.subject),
                subtitle: entry.topic != null ? Text(entry.topic!) : null,
                trailing: Text(
                  '${entry.durationMinutes ?? 0}dk',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )),
          ],
        ),
      ),
    );
  }

  void _showStartDialog(BuildContext context, TimeTrackingProvider provider) {
    final subjectController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pomodoro Başlat'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: subjectController,
              decoration: const InputDecoration(
                labelText: 'Çalışılacak Konu',
                hintText: 'Örn: Matematik - Türev',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              final subject = subjectController.text.trim();
              if (subject.isNotEmpty) {
                provider.startTimer(subject: subject);
                Navigator.pop(context);
              }
            },
            child: const Text('Başlat'),
          ),
        ],
      ),
    );
  }
}
