package handlers

import (
	"net/http"
	"strconv"
	"study-tracker-backend/database"
	"study-tracker-backend/models"

	"github.com/gin-gonic/gin"
)

// GetSubjects retrieves all subjects, optionally filtered by class_id
func GetSubjects(c *gin.Context) {
	classID := c.Query("class_id")

	var query string
	var args []interface{}

	if classID != "" {
		query = `
			SELECT s.id, s.name, s.description, s.class_id, c.name as class_name, s.created_at, s.updated_at
			FROM subjects s
			LEFT JOIN classes c ON s.class_id = c.id
			WHERE s.class_id = ?
			ORDER BY s.name
		`
		args = append(args, classID)
	} else {
		query = `
			SELECT s.id, s.name, s.description, s.class_id, c.name as class_name, s.created_at, s.updated_at
			FROM subjects s
			LEFT JOIN classes c ON s.class_id = c.id
			ORDER BY c.name, s.name
		`
	}

	rows, err := database.DB.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subjects"})
		return
	}
	defer rows.Close()

	var subjects []models.Subject
	for rows.Next() {
		var subject models.Subject
		err := rows.Scan(&subject.ID, &subject.Name, &subject.Description, &subject.ClassID, &subject.ClassName, &subject.CreatedAt, &subject.UpdatedAt)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan subject"})
			return
		}
		subjects = append(subjects, subject)
	}

	c.JSON(http.StatusOK, gin.H{"data": subjects})
}

// GetSubject retrieves a single subject by ID
func GetSubject(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subject ID"})
		return
	}

	query := `
		SELECT s.id, s.name, s.description, s.class_id, c.name as class_name, s.created_at, s.updated_at
		FROM subjects s
		LEFT JOIN classes c ON s.class_id = c.id
		WHERE s.id = ?
	`
	var subject models.Subject
	err = database.DB.QueryRow(query, id).Scan(&subject.ID, &subject.Name, &subject.Description, &subject.ClassID, &subject.ClassName, &subject.CreatedAt, &subject.UpdatedAt)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subject not found"})
		return
	}

	c.JSON(http.StatusOK, subject)
}

// CreateSubject creates a new subject
func CreateSubject(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		ClassID     int    `json:"class_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `INSERT INTO subjects (name, description, class_id) VALUES (?, ?, ?)`
	result, err := database.DB.Exec(query, req.Name, req.Description, req.ClassID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subject"})
		return
	}

	id, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, gin.H{"id": id, "message": "Subject created successfully"})
}

// UpdateSubject updates an existing subject
func UpdateSubject(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subject ID"})
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		ClassID     int    `json:"class_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `UPDATE subjects SET name = ?, description = ?, class_id = ? WHERE id = ?`
	result, err := database.DB.Exec(query, req.Name, req.Description, req.ClassID, id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update subject"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subject not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subject updated successfully"})
}

// DeleteSubject deletes a subject
func DeleteSubject(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subject ID"})
		return
	}

	query := `DELETE FROM subjects WHERE id = ?`
	result, err := database.DB.Exec(query, id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete subject"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subject not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subject deleted successfully"})
}
