import 'package:flutter/material.dart';
import 'registration_screen.dart';
import '../../models/user.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: <PERSON><PERSON><PERSON>(
            children: [
              const SizedBox(height: 40),
              // Logo ve animasyon
              Container(
                height: 200,
                width: 200,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.school,
                  size: 100,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 32),
              
              // Başlık
              Text(
                'StudyCoach AI\'ya\nHoş Geldin!',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 16),
              
              // Alt başlık
              Text(
                'Ortaokul ve lise hayatında başarılı olmak için kişiselleştirilmiş çalışma planları, akıllı hedef takibi ve AI koçluk desteği!',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 48),
              
              // Özellikler listesi
              _buildFeatureList(context),
              const SizedBox(height: 40),
              
              // Kullanıcı türü seçimi
              Text(
                'Kimsin?',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),

              // Öğrenci butonu
              _buildUserTypeButton(
                context,
                icon: Icons.school,
                title: 'Öğrenci',
                subtitle: 'Çalışma planları ve AI koçluk',
                color: Colors.blue,
                onTap: () => _navigateToRegistration(context, UserType.student),
              ),
              const SizedBox(height: 16),

              // Öğretmen butonu
              _buildUserTypeButton(
                context,
                icon: Icons.person_outline,
                title: 'Öğretmen',
                subtitle: 'Öğrenci takibi ve raporlama',
                color: Colors.green,
                onTap: () => _navigateToRegistration(context, UserType.teacher),
              ),
              const SizedBox(height: 16),

              // Veli butonu
              _buildUserTypeButton(
                context,
                icon: Icons.family_restroom,
                title: 'Veli',
                subtitle: 'Çocuğunuzun ilerlemesini takip edin',
                color: Colors.orange,
                onTap: () => _navigateToRegistration(context, UserType.parent),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureList(BuildContext context) {
    final features = [
      {
        'icon': Icons.calendar_today,
        'title': 'Akıllı Planlama',
        'description': 'Kişiselleştirilmiş çalışma programları',
      },
      {
        'icon': Icons.flag,
        'title': 'Hedef Takibi',
        'description': 'İlerlemenizi görsel olarak takip edin',
      },
      {
        'icon': Icons.timer,
        'title': 'Pomodoro Timer',
        'description': 'Verimli çalışma seansları',
      },
      {
        'icon': Icons.smart_toy,
        'title': 'AI Koç',
        'description': '7/24 kişisel çalışma danışmanı',
      },
    ];

    return Column(
      children: features.map((feature) => 
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      feature['description'] as String,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ).toList(),
    );
  }

  Widget _buildUserTypeButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: color,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToRegistration(BuildContext context, UserType userType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegistrationScreen(userType: userType),
      ),
    );
  }
}
