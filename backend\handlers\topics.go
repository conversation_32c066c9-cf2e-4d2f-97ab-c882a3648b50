package handlers

import (
	"net/http"
	"strconv"
	"study-tracker-backend/database"
	"study-tracker-backend/models"

	"github.com/gin-gonic/gin"
)

// GetTopics retrieves all topics, optionally filtered by subject_id
func GetTopics(c *gin.Context) {
	subjectID := c.Query("subject_id")
	
	var query string
	var args []interface{}
	
	if subjectID != "" {
		query = `
			SELECT t.id, t.name, t.description, t.subject_id, s.name as subject_name, t.order_index, t.created_at, t.updated_at 
			FROM topics t
			LEFT JOIN subjects s ON t.subject_id = s.id
			WHERE t.subject_id = ?
			ORDER BY t.order_index, t.name
		`
		args = append(args, subjectID)
	} else {
		query = `
			SELECT t.id, t.name, t.description, t.subject_id, s.name as subject_name, t.order_index, t.created_at, t.updated_at 
			FROM topics t
			LEFT JOIN subjects s ON t.subject_id = s.id
			ORDER BY s.name, t.order_index, t.name
		`
	}
	
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch topics"})
		return
	}
	defer rows.Close()

	var topics []models.Topic
	for rows.Next() {
		var topic models.Topic
		err := rows.Scan(&topic.ID, &topic.Name, &topic.Description, &topic.SubjectID, &topic.SubjectName, &topic.OrderIndex, &topic.CreatedAt, &topic.UpdatedAt)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan topic"})
			return
		}
		topics = append(topics, topic)
	}

	c.JSON(http.StatusOK, gin.H{"data": topics})
}

// GetTopic retrieves a single topic by ID
func GetTopic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid topic ID"})
		return
	}

	query := `
		SELECT t.id, t.name, t.description, t.subject_id, s.name as subject_name, t.order_index, t.created_at, t.updated_at 
		FROM topics t
		LEFT JOIN subjects s ON t.subject_id = s.id
		WHERE t.id = ?
	`
	var topic models.Topic
	err = database.DB.QueryRow(query, id).Scan(&topic.ID, &topic.Name, &topic.Description, &topic.SubjectID, &topic.SubjectName, &topic.OrderIndex, &topic.CreatedAt, &topic.UpdatedAt)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	c.JSON(http.StatusOK, topic)
}

// CreateTopic creates a new topic
func CreateTopic(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		SubjectID   int    `json:"subject_id" binding:"required"`
		OrderIndex  int    `json:"order_index"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `INSERT INTO topics (name, description, subject_id, order_index) VALUES (?, ?, ?, ?)`
	result, err := database.DB.Exec(query, req.Name, req.Description, req.SubjectID, req.OrderIndex)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create topic"})
		return
	}

	id, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, gin.H{"id": id, "message": "Topic created successfully"})
}

// UpdateTopic updates an existing topic
func UpdateTopic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid topic ID"})
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		SubjectID   int    `json:"subject_id" binding:"required"`
		OrderIndex  int    `json:"order_index"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `UPDATE topics SET name = ?, description = ?, subject_id = ?, order_index = ? WHERE id = ?`
	result, err := database.DB.Exec(query, req.Name, req.Description, req.SubjectID, req.OrderIndex, id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update topic"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Topic updated successfully"})
}

// DeleteTopic deletes a topic
func DeleteTopic(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid topic ID"})
		return
	}

	query := `DELETE FROM topics WHERE id = ?`
	result, err := database.DB.Exec(query, id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete topic"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Topic deleted successfully"})
}

// ReorderTopics updates the order of topics within a subject
func ReorderTopics(c *gin.Context) {
	var req struct {
		Topics []struct {
			ID         int `json:"id" binding:"required"`
			OrderIndex int `json:"order_index" binding:"required"`
		} `json:"topics" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Start transaction
	tx, err := database.DB.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
		return
	}
	defer tx.Rollback()

	// Update each topic's order
	for _, topic := range req.Topics {
		query := `UPDATE topics SET order_index = ? WHERE id = ?`
		_, err := tx.Exec(query, topic.OrderIndex, topic.ID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update topic order"})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Topics reordered successfully"})
}
