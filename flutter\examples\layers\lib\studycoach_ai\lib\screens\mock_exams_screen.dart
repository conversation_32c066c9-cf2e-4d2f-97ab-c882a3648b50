import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/mock_exam.dart';
import '../providers/user_provider.dart';
import 'mock_exam_add_screen.dart';

class MockExamsScreen extends StatefulWidget {
  const MockExamsScreen({super.key});

  @override
  State<MockExamsScreen> createState() => _MockExamsScreenState();
}

class _MockExamsScreenState extends State<MockExamsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ExamType? _selectedExamType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Deneme Sınavlarım'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Sınavlarım', icon: Icon(Icons.list)),
            Tab(text: 'Analiz', icon: Icon(Icons.analytics)),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MockExamAddScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildExamsTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildExamsTab() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        final exams = user?.mockExamProfile?.exams ?? [];

        if (exams.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // Filtre
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Text('Sınav Türü: '),
                  const SizedBox(width: 8),
                  Expanded(
                    child: DropdownButton<ExamType?>(
                      value: _selectedExamType,
                      isExpanded: true,
                      items: [
                        const DropdownMenuItem<ExamType?>(
                          value: null,
                          child: Text('Tümü'),
                        ),
                        ...ExamType.values.map((type) => 
                          DropdownMenuItem<ExamType?>(
                            value: type,
                            child: Text(type.displayName),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedExamType = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            
            // Sınav listesi
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _getFilteredExams(exams).length,
                itemBuilder: (context, index) {
                  final exam = _getFilteredExams(exams)[index];
                  return _buildExamCard(exam);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnalysisTab() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;
        final profile = user?.mockExamProfile;

        if (profile == null || profile.exams.isEmpty) {
          return _buildEmptyAnalysisState();
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOverallStats(profile),
              const SizedBox(height: 24),
              _buildProgressChart(profile),
              const SizedBox(height: 24),
              _buildSubjectAnalysis(profile),
              const SizedBox(height: 24),
              _buildRecommendations(profile),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.quiz_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Henüz deneme sınavı eklenmedi',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'İlk deneme sınavını ekleyerek başla',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MockExamAddScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Deneme Sınavı Ekle'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAnalysisState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Analiz için en az 2 deneme sınavı gerekli',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Daha fazla sınav ekleyerek ilerleme analizini gör',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamCard(MockExam exam) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showExamDetails(exam),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getExamTypeColor(exam.examType),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      exam.examType.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${exam.examDate.day}/${exam.examDate.month}/${exam.examDate.year}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MockExamAddScreen(examToEdit: exam),
                          ),
                        );
                      } else if (value == 'delete') {
                        _deleteExam(exam);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('Düzenle'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Sil', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                exam.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildStatChip(
                    'Net',
                    exam.totalNet.toStringAsFixed(1),
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  _buildStatChip(
                    'Başarı',
                    '${exam.totalSuccessRate.toStringAsFixed(1)}%',
                    Colors.green,
                  ),
                  if (exam.ranking != null) ...[
                    const SizedBox(width: 8),
                    _buildStatChip(
                      'Sıralama',
                      '${exam.ranking}',
                      Colors.orange,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<MockExam> _getFilteredExams(List<MockExam> exams) {
    if (_selectedExamType == null) {
      return exams..sort((a, b) => b.examDate.compareTo(a.examDate));
    }
    return exams
        .where((exam) => exam.examType == _selectedExamType)
        .toList()
      ..sort((a, b) => b.examDate.compareTo(a.examDate));
  }

  Color _getExamTypeColor(ExamType type) {
    switch (type) {
      case ExamType.lgs:
        return Colors.purple;
      case ExamType.tyt:
        return Colors.blue;
      case ExamType.ayt:
        return Colors.green;
      case ExamType.msu:
        return Colors.orange;
      case ExamType.custom:
        return Colors.grey;
    }
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverallStats(MockExamProfile profile) {
    final totalExams = profile.exams.length;
    final avgNet = profile.getAverageNet();
    final avgSuccess = profile.getAverageSuccessRate();
    final recentExams = profile.getRecentExams(limit: 5);
    final improvement = recentExams.length >= 2
        ? recentExams.first.totalNet - recentExams.last.totalNet
        : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Genel İstatistikler',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Toplam Sınav',
                    totalExams.toString(),
                    Icons.quiz,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Ortalama Net',
                    avgNet.toStringAsFixed(1),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Ortalama Başarı',
                    '${avgSuccess.toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Son Gelişim',
                    '${improvement >= 0 ? '+' : ''}${improvement.toStringAsFixed(1)}',
                    improvement >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                    improvement >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressChart(MockExamProfile profile) {
    final recentExams = profile.getRecentExams(limit: 10).reversed.toList();

    if (recentExams.length < 2) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'İlerleme Grafiği',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < recentExams.length) {
                            final exam = recentExams[index];
                            return Text(
                              '${exam.examDate.day}/${exam.examDate.month}',
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: recentExams.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.totalNet);
                      }).toList(),
                      isCurved: true,
                      color: Theme.of(context).colorScheme.primary,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectAnalysis(MockExamProfile profile) {
    final subjectAverages = profile.getSubjectAverages();

    if (subjectAverages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ders Bazlı Analiz',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...subjectAverages.entries.map((entry) {
              final subject = entry.key;
              final average = entry.value;
              final color = _getSubjectColor(average);

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        subject,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: LinearProgressIndicator(
                        value: average / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(color),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${average.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendations(MockExamProfile profile) {
    final recommendations = _generateRecommendations(profile);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Öneriler',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...recommendations.map((recommendation) =>
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.blue[700]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        recommendation,
                        style: TextStyle(color: Colors.blue[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor(double average) {
    if (average >= 80) return Colors.green;
    if (average >= 60) return Colors.orange;
    return Colors.red;
  }

  List<String> _generateRecommendations(MockExamProfile profile) {
    final recommendations = <String>[];
    final subjectAverages = profile.getSubjectAverages();
    final recentExams = profile.getRecentExams(limit: 3);

    // Düşük performans gösteren dersler
    final weakSubjects = subjectAverages.entries
        .where((entry) => entry.value < 60)
        .map((entry) => entry.key)
        .toList();

    if (weakSubjects.isNotEmpty) {
      recommendations.add(
        'Bu derslere daha fazla odaklan: ${weakSubjects.join(', ')}'
      );
    }

    // İlerleme analizi
    if (recentExams.length >= 2) {
      final improvement = recentExams.first.totalNet - recentExams.last.totalNet;
      if (improvement > 0) {
        recommendations.add('Harika! Son sınavlarda ${improvement.toStringAsFixed(1)} net artış gösterdin.');
      } else if (improvement < 0) {
        recommendations.add('Son sınavlarda düşüş var. Çalışma stratejini gözden geçir.');
      }
    }

    // Genel öneriler
    final avgNet = profile.getAverageNet();
    if (avgNet < 30) {
      recommendations.add('Temel konuları tekrar et ve eksik konularını belirle.');
    } else if (avgNet < 60) {
      recommendations.add('Orta seviye konulara odaklan ve deneme sınavı sayısını artır.');
    } else {
      recommendations.add('İyi gidiyorsun! Zor konulara odaklanarak hedefini yükselt.');
    }

    return recommendations;
  }

  void _showExamDetails(MockExam exam) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(exam.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Sınav Türü', exam.examType.displayName),
              _buildDetailRow('Tarih', '${exam.examDate.day}/${exam.examDate.month}/${exam.examDate.year}'),
              _buildDetailRow('Toplam Net', exam.totalNet.toStringAsFixed(1)),
              _buildDetailRow('Başarı Oranı', '${exam.totalSuccessRate.toStringAsFixed(1)}%'),
              if (exam.ranking != null)
                _buildDetailRow('Sıralama', '${exam.ranking}/${exam.totalParticipants ?? '?'}'),
              if (exam.examCenter != null)
                _buildDetailRow('Sınav Merkezi', exam.examCenter!),
              const SizedBox(height: 16),
              const Text(
                'Ders Sonuçları:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...exam.subjectResults.map((result) =>
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Expanded(child: Text(result.subjectName)),
                      Text('${result.correctAnswers}D ${result.wrongAnswers}Y ${result.emptyAnswers}B'),
                    ],
                  ),
                ),
              ),
              if (exam.notes != null) ...[
                const SizedBox(height: 16),
                const Text(
                  'Notlar:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(exam.notes!),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Kapat'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _deleteExam(MockExam exam) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sınavı Sil'),
        content: Text('${exam.name} sınavını silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              final userProvider = Provider.of<UserProvider>(context, listen: false);
              final user = userProvider.currentUser;

              if (user?.mockExamProfile != null) {
                final updatedProfile = user!.mockExamProfile!.removeExam(exam.id);
                await userProvider.updateProfile(mockExamProfile: updatedProfile);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sınav başarıyla silindi'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            child: const Text('Sil', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
