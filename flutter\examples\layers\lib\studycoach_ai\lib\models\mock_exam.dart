
enum ExamType {
  lgs,
  tyt,
  ayt,
  msu,
  custom,
}

extension ExamTypeExtension on ExamType {
  String get displayName {
    switch (this) {
      case ExamType.lgs:
        return 'LGS';
      case ExamType.tyt:
        return 'TYT';
      case ExamType.ayt:
        return 'AYT';
      case ExamType.msu:
        return 'MSÜ';
      case ExamType.custom:
        return 'Özel Deneme';
    }
  }

  String get description {
    switch (this) {
      case ExamType.lgs:
        return 'Liselere Geçiş Sınavı';
      case ExamType.tyt:
        return 'Temel Yeterlilik Testi';
      case ExamType.ayt:
        return '<PERSON>';
      case ExamType.msu:
        return 'Milli Savunma Üniversitesi';
      case ExamType.custom:
        return 'Özel Deneme Sınavı';
    }
  }

  List<String> get subjects {
    switch (this) {
      case ExamType.lgs:
        return ['Türkçe', 'Matematik', 'Fen Bilimleri', 'T.C. İnkılap Tarihi ve Atatürkçülük', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>'];
      case ExamType.tyt:
        return ['Türk<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON> Bilimleri', 'Sosyal Bilimler'];
      case ExamType.ayt:
        return ['Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Edebiyat', 'Tarih', 'Coğrafya', 'Felsefe', 'Din Kültürü'];
      case ExamType.msu:
        return ['Türkçe', 'Matematik', 'Fen Bilimleri', 'Sosyal Bilimler'];
      case ExamType.custom:
        return [];
    }
  }

  int get totalQuestions {
    switch (this) {
      case ExamType.lgs:
        return 90; // 20+20+20+10+10+10
      case ExamType.tyt:
        return 120; // 40+40+20+20
      case ExamType.ayt:
        return 80; // Değişken, alan bazlı
      case ExamType.msu:
        return 120;
      case ExamType.custom:
        return 0;
    }
  }

  Map<String, int> get subjectQuestionCounts {
    switch (this) {
      case ExamType.lgs:
        return {
          'Türkçe': 20,
          'Matematik': 20,
          'Fen Bilimleri': 20,
          'T.C. İnkılap Tarihi ve Atatürkçülük': 10,
          'İngilizce': 10,
          'Din Kültürü': 10,
        };
      case ExamType.tyt:
        return {
          'Türkçe': 40,
          'Matematik': 40,
          'Fen Bilimleri': 20,
          'Sosyal Bilimler': 20,
        };
      case ExamType.ayt:
        return {
          'Matematik': 40,
          'Fizik': 14,
          'Kimya': 13,
          'Biyoloji': 13,
          'Edebiyat': 24,
          'Tarih': 11,
          'Coğrafya': 11,
          'Felsefe': 12,
          'Din Kültürü': 6,
        };
      case ExamType.msu:
        return {
          'Türkçe': 30,
          'Matematik': 30,
          'Fen Bilimleri': 30,
          'Sosyal Bilimler': 30,
        };
      case ExamType.custom:
        return {};
    }
  }
}

class SubjectResult {
  final String subjectName;
  final int correctAnswers;
  final int wrongAnswers;
  final int emptyAnswers;
  final int totalQuestions;

  SubjectResult({
    required this.subjectName,
    required this.correctAnswers,
    required this.wrongAnswers,
    required this.emptyAnswers,
    required this.totalQuestions,
  });

  double get successRate => totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
  double get net => correctAnswers - (wrongAnswers * 0.25);

  Map<String, dynamic> toJson() {
    return {
      'subjectName': subjectName,
      'correctAnswers': correctAnswers,
      'wrongAnswers': wrongAnswers,
      'emptyAnswers': emptyAnswers,
      'totalQuestions': totalQuestions,
    };
  }

  factory SubjectResult.fromJson(Map<String, dynamic> json) {
    return SubjectResult(
      subjectName: json['subjectName'],
      correctAnswers: json['correctAnswers'],
      wrongAnswers: json['wrongAnswers'],
      emptyAnswers: json['emptyAnswers'],
      totalQuestions: json['totalQuestions'],
    );
  }
}

class MockExam {
  final String id;
  final String name;
  final ExamType examType;
  final DateTime examDate;
  final List<SubjectResult> subjectResults;
  final int? ranking;
  final int? totalParticipants;
  final String? examCenter;
  final String? notes;
  final DateTime createdAt;

  MockExam({
    required this.id,
    required this.name,
    required this.examType,
    required this.examDate,
    required this.subjectResults,
    this.ranking,
    this.totalParticipants,
    this.examCenter,
    this.notes,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  double get totalNet {
    return subjectResults.fold(0.0, (sum, result) => sum + result.net);
  }

  double get totalSuccessRate {
    if (subjectResults.isEmpty) return 0;
    final totalCorrect = subjectResults.fold(0, (sum, result) => sum + result.correctAnswers);
    final totalQuestions = subjectResults.fold(0, (sum, result) => sum + result.totalQuestions);
    return totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0;
  }

  int get totalCorrectAnswers {
    return subjectResults.fold(0, (sum, result) => sum + result.correctAnswers);
  }

  int get totalWrongAnswers {
    return subjectResults.fold(0, (sum, result) => sum + result.wrongAnswers);
  }

  int get totalEmptyAnswers {
    return subjectResults.fold(0, (sum, result) => sum + result.emptyAnswers);
  }

  int get totalQuestions {
    return subjectResults.fold(0, (sum, result) => sum + result.totalQuestions);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'examType': examType.toString(),
      'examDate': examDate.toIso8601String(),
      'subjectResults': subjectResults.map((r) => r.toJson()).toList(),
      'ranking': ranking,
      'totalParticipants': totalParticipants,
      'examCenter': examCenter,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory MockExam.fromJson(Map<String, dynamic> json) {
    return MockExam(
      id: json['id'],
      name: json['name'],
      examType: ExamType.values.firstWhere(
        (e) => e.toString() == json['examType'],
      ),
      examDate: DateTime.parse(json['examDate']),
      subjectResults: (json['subjectResults'] as List)
          .map((r) => SubjectResult.fromJson(r))
          .toList(),
      ranking: json['ranking'],
      totalParticipants: json['totalParticipants'],
      examCenter: json['examCenter'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  MockExam copyWith({
    String? id,
    String? name,
    ExamType? examType,
    DateTime? examDate,
    List<SubjectResult>? subjectResults,
    int? ranking,
    int? totalParticipants,
    String? examCenter,
    String? notes,
    DateTime? createdAt,
  }) {
    return MockExam(
      id: id ?? this.id,
      name: name ?? this.name,
      examType: examType ?? this.examType,
      examDate: examDate ?? this.examDate,
      subjectResults: subjectResults ?? this.subjectResults,
      ranking: ranking ?? this.ranking,
      totalParticipants: totalParticipants ?? this.totalParticipants,
      examCenter: examCenter ?? this.examCenter,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class MockExamProfile {
  final List<MockExam> exams;
  final DateTime lastUpdated;

  MockExamProfile({
    this.exams = const [],
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  List<MockExam> getExamsByType(ExamType type) {
    return exams.where((exam) => exam.examType == type).toList()
      ..sort((a, b) => b.examDate.compareTo(a.examDate));
  }

  List<MockExam> getRecentExams({int limit = 5}) {
    final sortedExams = List<MockExam>.from(exams)
      ..sort((a, b) => b.examDate.compareTo(a.examDate));
    return sortedExams.take(limit).toList();
  }

  double getAverageSuccessRate({ExamType? examType}) {
    final filteredExams = examType != null 
        ? getExamsByType(examType)
        : exams;
    
    if (filteredExams.isEmpty) return 0;
    
    final totalRate = filteredExams.fold(0.0, (sum, exam) => sum + exam.totalSuccessRate);
    return totalRate / filteredExams.length;
  }

  double getAverageNet({ExamType? examType}) {
    final filteredExams = examType != null 
        ? getExamsByType(examType)
        : exams;
    
    if (filteredExams.isEmpty) return 0;
    
    final totalNet = filteredExams.fold(0.0, (sum, exam) => sum + exam.totalNet);
    return totalNet / filteredExams.length;
  }

  Map<String, double> getSubjectAverages({ExamType? examType}) {
    final filteredExams = examType != null 
        ? getExamsByType(examType)
        : exams;
    
    if (filteredExams.isEmpty) return {};
    
    final subjectTotals = <String, List<double>>{};
    
    for (final exam in filteredExams) {
      for (final result in exam.subjectResults) {
        if (!subjectTotals.containsKey(result.subjectName)) {
          subjectTotals[result.subjectName] = [];
        }
        subjectTotals[result.subjectName]!.add(result.successRate);
      }
    }
    
    final averages = <String, double>{};
    subjectTotals.forEach((subject, rates) {
      averages[subject] = rates.fold(0.0, (sum, rate) => sum + rate) / rates.length;
    });
    
    return averages;
  }

  Map<String, dynamic> toJson() {
    return {
      'exams': exams.map((e) => e.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory MockExamProfile.fromJson(Map<String, dynamic> json) {
    return MockExamProfile(
      exams: (json['exams'] as List?)
          ?.map((e) => MockExam.fromJson(e))
          .toList() ?? [],
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'])
          : DateTime.now(),
    );
  }

  MockExamProfile addExam(MockExam exam) {
    final updatedExams = List<MockExam>.from(exams)..add(exam);
    return MockExamProfile(
      exams: updatedExams,
      lastUpdated: DateTime.now(),
    );
  }

  MockExamProfile removeExam(String examId) {
    final updatedExams = exams.where((exam) => exam.id != examId).toList();
    return MockExamProfile(
      exams: updatedExams,
      lastUpdated: DateTime.now(),
    );
  }

  MockExamProfile updateExam(MockExam updatedExam) {
    final updatedExams = exams.map((exam) {
      return exam.id == updatedExam.id ? updatedExam : exam;
    }).toList();
    
    return MockExamProfile(
      exams: updatedExams,
      lastUpdated: DateTime.now(),
    );
  }
}
