class StudyPlan {
  final String id;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final List<StudySession> sessions;
  final bool isCompleted;

  StudyPlan({
    required this.id,
    required this.title,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.sessions,
    this.isCompleted = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'sessions': sessions.map((s) => s.toJson()).toList(),
      'isCompleted': isCompleted,
    };
  }

  factory StudyPlan.fromJson(Map<String, dynamic> json) {
    return StudyPlan(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      sessions: (json['sessions'] as List)
          .map((s) => StudySession.fromJson(s))
          .toList(),
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}

class StudySession {
  final String id;
  final String subject;
  final String topic;
  final DateTime scheduledTime;
  final int durationMinutes;
  final bool isCompleted;
  final String? notes;

  StudySession({
    required this.id,
    required this.subject,
    required this.topic,
    required this.scheduledTime,
    required this.durationMinutes,
    this.isCompleted = false,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subject': subject,
      'topic': topic,
      'scheduledTime': scheduledTime.toIso8601String(),
      'durationMinutes': durationMinutes,
      'isCompleted': isCompleted,
      'notes': notes,
    };
  }

  factory StudySession.fromJson(Map<String, dynamic> json) {
    return StudySession(
      id: json['id'],
      subject: json['subject'],
      topic: json['topic'],
      scheduledTime: DateTime.parse(json['scheduledTime']),
      durationMinutes: json['durationMinutes'],
      isCompleted: json['isCompleted'] ?? false,
      notes: json['notes'],
    );
  }
}
