package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"strconv"
	"time"

	"golang.org/x/crypto/bcrypt"
	"github.com/golang-jwt/jwt/v5"
	"study-tracker-backend/models"
)

// HashPassword hashes a password using bcrypt
func HashPassword(password string) (string, error) {
	cost := getBcryptCost()
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %v", err)
	}
	return string(hashedBytes), nil
}

// CheckPassword compares a password with its hash
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateRandomToken generates a random token for password reset or email verification
func GenerateRandomToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random token: %v", err)
	}
	return hex.EncodeToString(bytes), nil
}

// GenerateJWT generates a JWT token for the user
func GenerateJWT(user *models.User) (string, string, error) {
	jwtSecret := getJWTSecret()
	accessTokenExpiry := getAccessTokenExpiry()
	refreshTokenExpiry := getRefreshTokenExpiry()

	// Generate access token
	accessClaims := &models.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		UserType: user.UserType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(accessTokenExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "study-tracker",
			Subject:   strconv.Itoa(user.ID),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %v", err)
	}

	// Generate refresh token
	refreshClaims := &models.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		UserType: user.UserType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(refreshTokenExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "study-tracker",
			Subject:   strconv.Itoa(user.ID),
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %v", err)
	}

	return accessTokenString, refreshTokenString, nil
}

// ValidateJWT validates a JWT token and returns the claims
func ValidateJWT(tokenString string) (*models.JWTClaims, error) {
	jwtSecret := getJWTSecret()

	token, err := jwt.ParseWithClaims(tokenString, &models.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %v", err)
	}

	if claims, ok := token.Claims.(*models.JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// IsPasswordStrong checks if a password meets security requirements
func IsPasswordStrong(password string) (bool, string) {
	if len(password) < 6 {
		return false, "Password must be at least 6 characters long"
	}
	
	if len(password) > 128 {
		return false, "Password must be less than 128 characters long"
	}

	// Add more password strength checks as needed
	// hasUpper := false
	// hasLower := false
	// hasDigit := false
	// hasSpecial := false

	// for _, char := range password {
	// 	switch {
	// 	case unicode.IsUpper(char):
	// 		hasUpper = true
	// 	case unicode.IsLower(char):
	// 		hasLower = true
	// 	case unicode.IsDigit(char):
	// 		hasDigit = true
	// 	case unicode.IsPunct(char) || unicode.IsSymbol(char):
	// 		hasSpecial = true
	// 	}
	// }

	// if !hasUpper || !hasLower || !hasDigit {
	// 	return false, "Password must contain at least one uppercase letter, one lowercase letter, and one digit"
	// }

	return true, ""
}

// Helper functions to get configuration values
func getBcryptCost() int {
	if cost := os.Getenv("BCRYPT_COST"); cost != "" {
		if c, err := strconv.Atoi(cost); err == nil && c >= 4 && c <= 31 {
			return c
		}
	}
	return bcrypt.DefaultCost
}

func getJWTSecret() string {
	if secret := os.Getenv("JWT_SECRET"); secret != "" {
		return secret
	}
	return "your-super-secret-jwt-key-change-this-in-production"
}

func getAccessTokenExpiry() time.Duration {
	if expiry := os.Getenv("ACCESS_TOKEN_EXPIRY"); expiry != "" {
		if d, err := time.ParseDuration(expiry); err == nil {
			return d
		}
	}
	return 24 * time.Hour // Default: 24 hours
}

func getRefreshTokenExpiry() time.Duration {
	if expiry := os.Getenv("REFRESH_TOKEN_EXPIRY"); expiry != "" {
		if d, err := time.ParseDuration(expiry); err == nil {
			return d
		}
	}
	return 7 * 24 * time.Hour // Default: 7 days
}
