{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\repository\\\\coach ai\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { Toaster } from 'react-hot-toast';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Students from './pages/Students';\nimport Classes from './pages/Classes';\nimport Subjects from './pages/Subjects';\nimport SubjectsManagement from './pages/SubjectsManagement';\nimport StudyRecords from './pages/StudyRecords';\nimport Analytics from './pages/Analytics';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient();\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/students\",\n            element: /*#__PURE__*/_jsxDEV(Students, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/classes\",\n            element: /*#__PURE__*/_jsxDEV(Classes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/subjects\",\n            element: /*#__PURE__*/_jsxDEV(Subjects, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/study-records\",\n            element: /*#__PURE__*/_jsxDEV(StudyRecords, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/analytics\",\n            element: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "QueryClient", "QueryClientProvider", "Toaster", "Layout", "Dashboard", "Students", "Classes", "Subjects", "SubjectsManagement", "StudyRecords", "Analytics", "jsxDEV", "_jsxDEV", "queryClient", "App", "client", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/repository/coach ai/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { Toaster } from 'react-hot-toast';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Students from './pages/Students';\nimport Classes from './pages/Classes';\nimport Subjects from './pages/Subjects';\nimport SubjectsManagement from './pages/SubjectsManagement';\nimport StudyRecords from './pages/StudyRecords';\nimport Analytics from './pages/Analytics';\n\nconst queryClient = new QueryClient();\n\nfunction App() {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <Layout>\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/students\" element={<Students />} />\n            <Route path=\"/classes\" element={<Classes />} />\n            <Route path=\"/subjects\" element={<Subjects />} />\n            <Route path=\"/study-records\" element={<StudyRecords />} />\n            <Route path=\"/analytics\" element={<Analytics />} />\n          </Routes>\n        </Layout>\n        <Toaster position=\"top-right\" />\n      </Router>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,GAAG,IAAIb,WAAW,CAAC,CAAC;AAErC,SAASc,GAAGA,CAAA,EAAG;EACb,oBACEF,OAAA,CAACX,mBAAmB;IAACc,MAAM,EAAEF,WAAY;IAAAG,QAAA,eACvCJ,OAAA,CAACf,MAAM;MAAAmB,QAAA,gBACLJ,OAAA,CAACT,MAAM;QAAAa,QAAA,eACLJ,OAAA,CAACd,MAAM;UAAAkB,QAAA,gBACLJ,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEN,OAAA,CAACR,SAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CV,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEN,OAAA,CAACP,QAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDV,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEN,OAAA,CAACN,OAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CV,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEN,OAAA,CAACL,QAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDV,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAEN,OAAA,CAACH,YAAY;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DV,OAAA,CAACb,KAAK;YAACkB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEN,OAAA,CAACF,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACTV,OAAA,CAACV,OAAO;QAACqB,QAAQ,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B;AAACE,EAAA,GAlBQV,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}