class TimeEntry {
  final String id;
  final String subject;
  final String? topic;
  final DateTime startTime;
  final DateTime? endTime;
  final int? durationMinutes;
  final String? notes;
  final bool isPomodoro;
  final int? pomodoroCount;

  TimeEntry({
    required this.id,
    required this.subject,
    this.topic,
    required this.startTime,
    this.endTime,
    this.durationMinutes,
    this.notes,
    this.isPomodoro = false,
    this.pomodoroCount,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subject': subject,
      'topic': topic,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'durationMinutes': durationMinutes,
      'notes': notes,
      'isPomodoro': isPomodoro,
      'pomodoroCount': pomodoroCount,
    };
  }

  factory TimeEntry.fromJson(Map<String, dynamic> json) {
    return TimeEntry(
      id: json['id'],
      subject: json['subject'],
      topic: json['topic'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      durationMinutes: json['durationMinutes'],
      notes: json['notes'],
      isPomodoro: json['isPomodoro'] ?? false,
      pomodoroCount: json['pomodoroCount'],
    );
  }

  TimeEntry copyWith({
    String? id,
    String? subject,
    String? topic,
    DateTime? startTime,
    DateTime? endTime,
    int? durationMinutes,
    String? notes,
    bool? isPomodoro,
    int? pomodoroCount,
  }) {
    return TimeEntry(
      id: id ?? this.id,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      notes: notes ?? this.notes,
      isPomodoro: isPomodoro ?? this.isPomodoro,
      pomodoroCount: pomodoroCount ?? this.pomodoroCount,
    );
  }
}
