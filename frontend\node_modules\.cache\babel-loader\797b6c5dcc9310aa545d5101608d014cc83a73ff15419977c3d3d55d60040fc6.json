{"ast": null, "code": "// Predefined subjects for Turkish education system\nexport const PREDEFINED_SUBJECTS = {\n  // İlkokul (5. Sınıf)\n  5: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON> Bilimleri', '<PERSON><PERSON>al Bilgiler', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'G<PERSON>rsel Sanatlar', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON> Eğitimi', '<PERSON> ve Ahlak Bilgisi'],\n  // Ortaokul (6-7. Sınıf)\n  6: ['<PERSON>ürk<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON> Bilimleri', '<PERSON><PERSON>al Bilgiler', '<PERSON>ng<PERSON><PERSON><PERSON>', 'Görsel Sanatlar', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>en Eğitimi', '<PERSON> ve Ahlak Bilgisi', 'Teknoloji ve Tasarım'],\n  7: ['<PERSON><PERSON>rk<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Fen Bilimleri', 'Sosyal Bilgiler', '<PERSON>ng<PERSON><PERSON><PERSON>', 'Görsel Sanatlar', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>ğ<PERSON>', '<PERSON> ve Ahlak Bilgisi', 'Teknoloji ve Tasarım'],\n  // 8. S<PERSON>n<PERSON>f (LGS)\n  8: ['T<PERSON>rk<PERSON><PERSON>', '<PERSON><PERSON><PERSON>k', 'Fen Bilimleri', 'T.C. İnkılap Tarihi ve Atatürkçülük', 'Din Kültürü ve Ahlak Bilgisi', 'İngilizce', 'Görsel Sanatlar', 'Müzik', 'Beden Eğitimi', 'Teknoloji ve Tasarım'],\n  // Lise (9-10. Sınıf)\n  9: ['Türk Dili ve Edebiyatı', 'Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Tarih', 'Coğrafya', 'Felsefe', 'İngilizce', 'Din Kültürü ve Ahlak Bilgisi', 'Beden Eğitimi', 'Görsel Sanatlar', 'Müzik'],\n  10: ['Türk Dili ve Edebiyatı', 'Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Tarih', 'Coğrafya', 'Felsefe', 'İngilizce', 'Din Kültürü ve Ahlak Bilgisi', 'Beden Eğitimi', 'Görsel Sanatlar', 'Müzik'],\n  // 11-12. Sınıf ve Mezun (YKS)\n  11: ['Türk Dili ve Edebiyatı', 'Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Tarih', 'Coğrafya', 'Felsefe', 'Din Kültürü ve Ahlak Bilgisi', 'İngilizce', 'Almanca', 'Fransızca'],\n  12: ['Türk Dili ve Edebiyatı', 'Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Tarih', 'Coğrafya', 'Felsefe', 'Din Kültürü ve Ahlak Bilgisi', 'İngilizce', 'Almanca', 'Fransızca'],\n  // Mezun\n  'Mezun': ['Türk Dili ve Edebiyatı', 'Matematik', 'Fizik', 'Kimya', 'Biyoloji', 'Tarih', 'Coğrafya', 'Felsefe', 'Din Kültürü ve Ahlak Bilgisi', 'İngilizce', 'Almanca', 'Fransızca']\n};\n\n// Get subjects for a specific class\nexport const getSubjectsForClass = className => {\n  // Extract class number from class name (e.g., \"5. Sınıf\" -> 5)\n  if (className === 'Mezun') {\n    return PREDEFINED_SUBJECTS['Mezun'] || [];\n  }\n  const classNumber = parseInt(className.split('.')[0]);\n  return PREDEFINED_SUBJECTS[classNumber] || [];\n};\n\n// Get all unique subjects across all classes\nexport const getAllSubjects = () => {\n  const allSubjects = new Set();\n  Object.values(PREDEFINED_SUBJECTS).forEach(subjects => {\n    subjects.forEach(subject => allSubjects.add(subject));\n  });\n  return Array.from(allSubjects).sort();\n};\n\n// Check if a subject is valid for a specific class\nexport const isSubjectValidForClass = (subjectName, className) => {\n  const validSubjects = getSubjectsForClass(className);\n  return validSubjects.includes(subjectName);\n};", "map": {"version": 3, "names": ["PREDEFINED_SUBJECTS", "getSubjectsForClass", "className", "classNumber", "parseInt", "split", "getAllSubjects", "allSubjects", "Set", "Object", "values", "for<PERSON>ach", "subjects", "subject", "add", "Array", "from", "sort", "isSubjectValidForClass", "subjectName", "validSubjects", "includes"], "sources": ["C:/Users/<USER>/source/repository/coach ai/frontend/src/data/predefinedSubjects.js"], "sourcesContent": ["// Predefined subjects for Turkish education system\nexport const PREDEFINED_SUBJECTS = {\n  // İlkokul (5. Sınıf)\n  5: [\n    '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON><PERSON>', \n    '<PERSON> Bilimleri',\n    '<PERSON><PERSON>al Bilgiler',\n    '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    'G<PERSON>rsel Sanatlar',\n    '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON> Eğitimi',\n    '<PERSON> ve Ahlak Bilgisi'\n  ],\n  \n  // Ortaokul (6-7. Sınıf)\n  6: [\n    '<PERSON>ürk<PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON> Bilimleri', \n    '<PERSON><PERSON>al Bilgiler',\n    '<PERSON>ng<PERSON><PERSON><PERSON>',\n    'Görsel Sanatlar',\n    '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON>en Eğitimi',\n    '<PERSON> ve Ahlak Bilgisi',\n    'Teknoloji ve Tasarım'\n  ],\n  \n  7: [\n    '<PERSON><PERSON>rk<PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON><PERSON>',\n    'Fen Bilimleri',\n    'Sosyal Bilgiler', \n    '<PERSON>ng<PERSON><PERSON><PERSON>',\n    'Görsel Sanatlar',\n    '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON>ğ<PERSON>',\n    '<PERSON> ve Ahlak Bilgisi',\n    'Teknoloji ve Tasarım'\n  ],\n  \n  // 8. S<PERSON>n<PERSON>f (LGS)\n  8: [\n    'T<PERSON>rk<PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>k',\n    'Fen Bilimleri',\n    'T.C. İnkılap Tarihi ve Atatürkçülük',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'İngilizce',\n    'Görsel Sanatlar',\n    'Müzik',\n    'Beden Eğitimi',\n    'Teknoloji ve Tasarım'\n  ],\n  \n  // Lise (9-10. Sınıf)\n  9: [\n    'Türk Dili ve Edebiyatı',\n    'Matematik',\n    'Fizik',\n    'Kimya',\n    'Biyoloji',\n    'Tarih',\n    'Coğrafya',\n    'Felsefe',\n    'İngilizce',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'Beden Eğitimi',\n    'Görsel Sanatlar',\n    'Müzik'\n  ],\n  \n  10: [\n    'Türk Dili ve Edebiyatı',\n    'Matematik',\n    'Fizik', \n    'Kimya',\n    'Biyoloji',\n    'Tarih',\n    'Coğrafya',\n    'Felsefe',\n    'İngilizce',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'Beden Eğitimi',\n    'Görsel Sanatlar',\n    'Müzik'\n  ],\n  \n  // 11-12. Sınıf ve Mezun (YKS)\n  11: [\n    'Türk Dili ve Edebiyatı',\n    'Matematik',\n    'Fizik',\n    'Kimya', \n    'Biyoloji',\n    'Tarih',\n    'Coğrafya',\n    'Felsefe',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'İngilizce',\n    'Almanca',\n    'Fransızca'\n  ],\n  \n  12: [\n    'Türk Dili ve Edebiyatı',\n    'Matematik',\n    'Fizik',\n    'Kimya',\n    'Biyoloji', \n    'Tarih',\n    'Coğrafya',\n    'Felsefe',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'İngilizce',\n    'Almanca',\n    'Fransızca'\n  ],\n  \n  // Mezun\n  'Mezun': [\n    'Türk Dili ve Edebiyatı',\n    'Matematik',\n    'Fizik',\n    'Kimya',\n    'Biyoloji',\n    'Tarih', \n    'Coğrafya',\n    'Felsefe',\n    'Din Kültürü ve Ahlak Bilgisi',\n    'İngilizce',\n    'Almanca',\n    'Fransızca'\n  ]\n};\n\n// Get subjects for a specific class\nexport const getSubjectsForClass = (className) => {\n  // Extract class number from class name (e.g., \"5. Sınıf\" -> 5)\n  if (className === 'Mezun') {\n    return PREDEFINED_SUBJECTS['Mezun'] || [];\n  }\n  \n  const classNumber = parseInt(className.split('.')[0]);\n  return PREDEFINED_SUBJECTS[classNumber] || [];\n};\n\n// Get all unique subjects across all classes\nexport const getAllSubjects = () => {\n  const allSubjects = new Set();\n  Object.values(PREDEFINED_SUBJECTS).forEach(subjects => {\n    subjects.forEach(subject => allSubjects.add(subject));\n  });\n  return Array.from(allSubjects).sort();\n};\n\n// Check if a subject is valid for a specific class\nexport const isSubjectValidForClass = (subjectName, className) => {\n  const validSubjects = getSubjectsForClass(className);\n  return validSubjects.includes(subjectName);\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,mBAAmB,GAAG;EACjC;EACA,CAAC,EAAE,CACD,QAAQ,EACR,WAAW,EACX,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,eAAe,EACf,8BAA8B,CAC/B;EAED;EACA,CAAC,EAAE,CACD,QAAQ,EACR,WAAW,EACX,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,eAAe,EACf,8BAA8B,EAC9B,sBAAsB,CACvB;EAED,CAAC,EAAE,CACD,QAAQ,EACR,WAAW,EACX,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,eAAe,EACf,8BAA8B,EAC9B,sBAAsB,CACvB;EAED;EACA,CAAC,EAAE,CACD,QAAQ,EACR,WAAW,EACX,eAAe,EACf,qCAAqC,EACrC,8BAA8B,EAC9B,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,eAAe,EACf,sBAAsB,CACvB;EAED;EACA,CAAC,EAAE,CACD,wBAAwB,EACxB,WAAW,EACX,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,WAAW,EACX,8BAA8B,EAC9B,eAAe,EACf,iBAAiB,EACjB,OAAO,CACR;EAED,EAAE,EAAE,CACF,wBAAwB,EACxB,WAAW,EACX,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,WAAW,EACX,8BAA8B,EAC9B,eAAe,EACf,iBAAiB,EACjB,OAAO,CACR;EAED;EACA,EAAE,EAAE,CACF,wBAAwB,EACxB,WAAW,EACX,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,8BAA8B,EAC9B,WAAW,EACX,SAAS,EACT,WAAW,CACZ;EAED,EAAE,EAAE,CACF,wBAAwB,EACxB,WAAW,EACX,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,8BAA8B,EAC9B,WAAW,EACX,SAAS,EACT,WAAW,CACZ;EAED;EACA,OAAO,EAAE,CACP,wBAAwB,EACxB,WAAW,EACX,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,8BAA8B,EAC9B,WAAW,EACX,SAAS,EACT,WAAW;AAEf,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAIC,SAAS,IAAK;EAChD;EACA,IAAIA,SAAS,KAAK,OAAO,EAAE;IACzB,OAAOF,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE;EAC3C;EAEA,MAAMG,WAAW,GAAGC,QAAQ,CAACF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,OAAOL,mBAAmB,CAACG,WAAW,CAAC,IAAI,EAAE;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMG,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7BC,MAAM,CAACC,MAAM,CAACV,mBAAmB,CAAC,CAACW,OAAO,CAACC,QAAQ,IAAI;IACrDA,QAAQ,CAACD,OAAO,CAACE,OAAO,IAAIN,WAAW,CAACO,GAAG,CAACD,OAAO,CAAC,CAAC;EACvD,CAAC,CAAC;EACF,OAAOE,KAAK,CAACC,IAAI,CAACT,WAAW,CAAC,CAACU,IAAI,CAAC,CAAC;AACvC,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,WAAW,EAAEjB,SAAS,KAAK;EAChE,MAAMkB,aAAa,GAAGnB,mBAAmB,CAACC,SAAS,CAAC;EACpD,OAAOkB,aAAa,CAACC,QAAQ,CAACF,WAAW,CAAC;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}