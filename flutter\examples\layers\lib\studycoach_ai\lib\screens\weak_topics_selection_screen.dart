import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/curriculum.dart';
import '../models/weak_topics.dart';
import '../models/user.dart';
import '../providers/user_provider.dart';

class WeakTopicsSelectionScreen extends StatefulWidget {
  const WeakTopicsSelectionScreen({super.key});

  @override
  State<WeakTopicsSelectionScreen> createState() => _WeakTopicsSelectionScreenState();
}

class _WeakTopicsSelectionScreenState extends State<WeakTopicsSelectionScreen> {
  late List<CurriculumSubject> availableSubjects;
  Map<String, List<WeakTopic>> selectedWeakTopics = {};
  String? selectedSubjectId;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final user = userProvider.currentUser;
    
    if (user != null) {
      availableSubjects = CurriculumData.getSubjectsForLevel(user.educationLevel);
      
      // Mevcut eksik konuları yükle
      if (user.weakTopicsProfile != null) {
        for (final weakTopic in user.weakTopicsProfile!.getActiveWeakTopics()) {
          if (!selectedWeakTopics.containsKey(weakTopic.subjectId)) {
            selectedWeakTopics[weakTopic.subjectId] = [];
          }
          selectedWeakTopics[weakTopic.subjectId]!.add(weakTopic);
        }
      }
      
      // İlk dersi seç
      if (availableSubjects.isNotEmpty) {
        selectedSubjectId = availableSubjects.first.id;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Eksik Konularım'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveWeakTopics,
            child: const Text(
              'Kaydet',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Ders seçimi
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Ders Seçin',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: availableSubjects.map((subject) => 
                      _buildSubjectChip(subject),
                    ).toList(),
                  ),
                ),
              ],
            ),
          ),
          
          // Konular listesi
          Expanded(
            child: selectedSubjectId != null
                ? _buildTopicsList()
                : const Center(
                    child: Text('Lütfen bir ders seçin'),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectChip(CurriculumSubject subject) {
    final isSelected = selectedSubjectId == subject.id;
    final selectedCount = selectedWeakTopics[subject.id]?.length ?? 0;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(subject.name),
            if (selectedCount > 0)
              Text(
                '$selectedCount konu',
                style: const TextStyle(fontSize: 10),
              ),
          ],
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            selectedSubjectId = subject.id;
          });
        },
        selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        checkmarkColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildTopicsList() {
    final topics = CurriculumData.getTopicsForSubject(selectedSubjectId!);
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: topics.length,
      itemBuilder: (context, index) {
        final topic = topics[index];
        final existingWeakTopic = selectedWeakTopics[selectedSubjectId!]
            ?.firstWhere((wt) => wt.topicId == topic.id, orElse: () => null as WeakTopic);
        
        return _buildTopicCard(topic, existingWeakTopic);
      },
    );
  }

  Widget _buildTopicCard(CurriculumTopic topic, WeakTopic? existingWeakTopic) {
    final isSelected = existingWeakTopic != null;
    final hasSubtopics = topic.subtopics.isNotEmpty;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      child: Column(
        children: [
          InkWell(
            onTap: () => _showTopicSelectionDialog(topic, existingWeakTopic),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          topic.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getWeaknessColor(existingWeakTopic.weaknessLevel),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                existingWeakTopic.weaknessLevel.displayName,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (hasSubtopics) ...[
                              const SizedBox(height: 4),
                              _buildMainTopicProgressIndicator(existingWeakTopic.progress, topic.subtopics.length),
                            ],
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    topic.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.schedule,
                        '${topic.estimatedHours} saat',
                        Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        Icons.trending_up,
                        'Zorluk: ${topic.difficulty}/5',
                        Colors.orange,
                      ),
                      if (hasSubtopics) ...[
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          Icons.list,
                          '${topic.subtopics.length} alt konu',
                          Colors.purple,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Alt konular listesi
          if (hasSubtopics && isSelected)
            _buildSubtopicsList(topic, existingWeakTopic),
        ],
      ),
    );
  }

  Widget _buildMainTopicProgressIndicator(TopicProgress progress, int totalSubtopics) {
    // Alt konuların ortalama ilerlemesini hesapla
    double averageProgress = 0.0;
    if (progress.subtopicProgresses.isNotEmpty) {
      final totalProgress = progress.subtopicProgresses
          .map((p) => p.completionPercentage)
          .reduce((a, b) => a + b);
      averageProgress = totalProgress / progress.subtopicProgresses.length;
    }

    return Column(
      children: [
        Container(
          width: 60,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: averageProgress / 100,
            child: Container(
              decoration: BoxDecoration(
                color: _getProgressColor(averageProgress),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '${averageProgress.toStringAsFixed(0)}%',
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSubtopicsList(CurriculumTopic topic, WeakTopic weakTopic) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Alt Konular',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const Spacer(),
              Text(
                '${_getCompletedSubtopicsCount(weakTopic.progress)}/${topic.subtopics.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...topic.subtopics.map((subtopic) =>
            _buildSubtopicItem(subtopic, weakTopic),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtopicItem(CurriculumSubtopic subtopic, WeakTopic weakTopic) {
    final isCompleted = weakTopic.progress.completedActivities.contains(subtopic.id);
    final subtopicProgress = weakTopic.progress.subtopicProgresses.firstWhere(
      (progress) => progress.subtopicId == subtopic.id,
      orElse: () => SubtopicProgress(
        subtopicId: subtopic.id,
        completionPercentage: 0,
        lastUpdated: DateTime.now(),
      ),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _showSubtopicProgressDialog(subtopic, weakTopic, subtopicProgress),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isCompleted ? Colors.green.withValues(alpha: 0.1) : Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isCompleted ? Colors.green.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                    size: 18,
                    color: isCompleted ? Colors.green : Colors.grey[400],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      subtopic.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isCompleted ? Colors.green[700] : Colors.grey[800],
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getProgressColor(subtopicProgress.completionPercentage).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${subtopicProgress.completionPercentage.toInt()}%',
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: _getProgressColor(subtopicProgress.completionPercentage),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // İlerleme çubuğu
              Container(
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: subtopicProgress.completionPercentage / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: _getProgressColor(subtopicProgress.completionPercentage),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 6),

              Row(
                children: [
                  Icon(Icons.schedule, size: 12, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    '${subtopic.estimatedHours} saat',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Icon(Icons.trending_up, size: 12, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    'Zorluk: ${subtopic.difficulty}/5',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(double percentage) {
    if (percentage < 30) return Colors.red;
    if (percentage < 70) return Colors.orange;
    return Colors.green;
  }

  Color _getWeaknessColor(WeaknessLevel level) {
    switch (level) {
      case WeaknessLevel.slight:
        return Colors.orange;
      case WeaknessLevel.moderate:
        return Colors.deepOrange;
      case WeaknessLevel.severe:
        return Colors.red;
    }
  }

  void _showTopicSelectionDialog(CurriculumTopic topic, WeakTopic? existingWeakTopic) {
    // Dialog implementation
  }

  void _showSubtopicProgressDialog(CurriculumSubtopic subtopic, WeakTopic weakTopic, SubtopicProgress subtopicProgress) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(subtopic.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              subtopic.description,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),

            // İlerleme bilgisi
            Row(
              children: [
                Text(
                  'İlerleme: ',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text('${subtopicProgress.completionPercentage.toInt()}%'),
              ],
            ),
            const SizedBox(height: 8),

            // İlerleme çubuğu
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: subtopicProgress.completionPercentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: _getProgressColor(subtopicProgress.completionPercentage),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Detaylar
            _buildDetailRow('Zorluk', '${subtopic.difficulty}/5'),
            _buildDetailRow('Tahmini Süre', '${subtopic.estimatedHours} saat'),
            _buildDetailRow('Son Güncelleme', _formatDate(subtopicProgress.lastUpdated)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Kapat'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showProgressUpdateDialog(subtopic, weakTopic, subtopicProgress);
            },
            child: const Text('İlerleme Güncelle'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showProgressUpdateDialog(CurriculumSubtopic subtopic, WeakTopic weakTopic, SubtopicProgress subtopicProgress) {
    double newProgress = subtopicProgress.completionPercentage;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('${subtopic.name} - İlerleme Güncelle'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Mevcut İlerleme: ${subtopicProgress.completionPercentage.toInt()}%',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),

              Text('Yeni İlerleme: ${newProgress.toInt()}%'),
              Slider(
                value: newProgress,
                min: 0,
                max: 100,
                divisions: 20,
                label: '${newProgress.toInt()}%',
                onChanged: (value) {
                  setState(() {
                    newProgress = value;
                  });
                },
              ),

              const SizedBox(height: 16),

              // İlerleme çubuğu önizlemesi
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: newProgress / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: _getProgressColor(newProgress),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('İptal'),
            ),
            ElevatedButton(
              onPressed: () {
                _updateSubtopicProgress(subtopic, weakTopic, newProgress);
                Navigator.of(context).pop();
              },
              child: const Text('Güncelle'),
            ),
          ],
        ),
      ),
    );
  }

  void _updateSubtopicProgress(CurriculumSubtopic subtopic, WeakTopic weakTopic, double newProgress) {
    // Bu metod gerçek uygulamada backend'e veri gönderecek
    // Şimdilik sadece UI'da gösterim için
    setState(() {
      // Mevcut subtopic progress'i güncelle veya yeni oluştur
      final existingIndex = weakTopic.progress.subtopicProgresses.indexWhere(
        (progress) => progress.subtopicId == subtopic.id,
      );

      if (existingIndex != -1) {
        // Mevcut progress'i güncelle
        final updatedProgress = weakTopic.progress.subtopicProgresses[existingIndex].copyWith(
          completionPercentage: newProgress,
          lastUpdated: DateTime.now(),
        );
        weakTopic.progress.subtopicProgresses[existingIndex] = updatedProgress;
      } else {
        // Yeni progress ekle
        final newSubtopicProgress = SubtopicProgress(
          subtopicId: subtopic.id,
          completionPercentage: newProgress,
          lastUpdated: DateTime.now(),
        );
        weakTopic.progress.subtopicProgresses.add(newSubtopicProgress);
      }

      // Ana konu ilerlemesini güncelle
      _updateMainTopicProgress(weakTopic);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${subtopic.name} ilerlemesi güncellendi'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _updateMainTopicProgress(WeakTopic weakTopic) {
    // Alt konuların ortalama ilerlemesini hesapla
    if (weakTopic.progress.subtopicProgresses.isNotEmpty) {
      final totalProgress = weakTopic.progress.subtopicProgresses
          .map((p) => p.completionPercentage)
          .reduce((a, b) => a + b);
      final averageProgress = totalProgress / weakTopic.progress.subtopicProgresses.length;

      // Ana konu ilerlemesini güncelle
      final completedCount = weakTopic.progress.subtopicProgresses
          .where((p) => p.completionPercentage >= 100)
          .length;

      // TopicProgress'i güncelle (bu gerçek uygulamada daha karmaşık olacak)
      // Şimdilik sadece UI güncellemesi için
    }
  }

  int _getCompletedSubtopicsCount(TopicProgress progress) {
    return progress.subtopicProgresses
        .where((p) => p.completionPercentage >= 100)
        .length;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Future<void> _saveWeakTopics() async {
    // Save implementation
  }
}
