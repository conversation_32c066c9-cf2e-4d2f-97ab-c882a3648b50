import 'package:flutter/foundation.dart';
import '../models/study_plan.dart';
import '../services/storage_service.dart';
import 'package:uuid/uuid.dart';

class StudyPlanProvider with ChangeNotifier {
  final StorageService _storageService = StorageService();
  final Uuid _uuid = const Uuid();
  
  List<StudyPlan> _studyPlans = [];
  bool _isLoading = false;

  List<StudyPlan> get studyPlans => _studyPlans;
  bool get isLoading => _isLoading;

  StudyPlanProvider() {
    loadStudyPlans();
  }

  Future<void> loadStudyPlans() async {
    _isLoading = true;
    notifyListeners();

    try {
      _studyPlans = await _storageService.getStudyPlans();
    } catch (e) {
      debugPrint('Error loading study plans: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> addStudyPlan(StudyPlan plan) async {
    try {
      final newPlan = StudyPlan(
        id: _uuid.v4(),
        title: plan.title,
        description: plan.description,
        startDate: plan.startDate,
        endDate: plan.endDate,
        sessions: plan.sessions,
      );

      _studyPlans.add(newPlan);
      await _storageService.saveStudyPlans(_studyPlans);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding study plan: $e');
    }
  }

  Future<void> updateStudyPlan(StudyPlan updatedPlan) async {
    try {
      final index = _studyPlans.indexWhere((plan) => plan.id == updatedPlan.id);
      if (index != -1) {
        _studyPlans[index] = updatedPlan;
        await _storageService.saveStudyPlans(_studyPlans);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating study plan: $e');
    }
  }

  Future<void> deleteStudyPlan(String planId) async {
    try {
      _studyPlans.removeWhere((plan) => plan.id == planId);
      await _storageService.saveStudyPlans(_studyPlans);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting study plan: $e');
    }
  }

  Future<void> completeSession(String planId, String sessionId) async {
    try {
      final planIndex = _studyPlans.indexWhere((plan) => plan.id == planId);
      if (planIndex != -1) {
        final plan = _studyPlans[planIndex];
        final updatedSessions = plan.sessions.map((session) {
          if (session.id == sessionId) {
            return StudySession(
              id: session.id,
              subject: session.subject,
              topic: session.topic,
              scheduledTime: session.scheduledTime,
              durationMinutes: session.durationMinutes,
              isCompleted: true,
              notes: session.notes,
            );
          }
          return session;
        }).toList();

        final updatedPlan = StudyPlan(
          id: plan.id,
          title: plan.title,
          description: plan.description,
          startDate: plan.startDate,
          endDate: plan.endDate,
          sessions: updatedSessions,
          isCompleted: plan.isCompleted,
        );

        await updateStudyPlan(updatedPlan);
      }
    } catch (e) {
      debugPrint('Error completing session: $e');
    }
  }

  List<StudySession> getTodaysSessions() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    List<StudySession> todaySessions = [];
    for (final plan in _studyPlans) {
      for (final session in plan.sessions) {
        if (session.scheduledTime.isAfter(todayStart) &&
            session.scheduledTime.isBefore(todayEnd)) {
          todaySessions.add(session);
        }
      }
    }

    todaySessions.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    return todaySessions;
  }

  List<StudySession> getWeekSessions() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 7));

    List<StudySession> weekSessions = [];
    for (final plan in _studyPlans) {
      for (final session in plan.sessions) {
        if (session.scheduledTime.isAfter(weekStart) &&
            session.scheduledTime.isBefore(weekEnd)) {
          weekSessions.add(session);
        }
      }
    }

    return weekSessions;
  }
}
