// Predefined subjects for Turkish education system
export const PREDEFINED_SUBJECTS = {
  // İlkokul (5. Sınıf)
  5: [
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>', 
    '<PERSON> Bilimleri',
    '<PERSON><PERSON>al Bilgiler',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'G<PERSON>rsel Sanatlar',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> Eğitimi',
    '<PERSON> ve Ahlak Bilgisi'
  ],
  
  // Ortaokul (6-7. Sınıf)
  6: [
    '<PERSON>ürk<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON> Bilimleri', 
    '<PERSON><PERSON>al Bilgiler',
    '<PERSON>ng<PERSON><PERSON><PERSON>',
    'Görsel Sanatlar',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>en Eğitimi',
    '<PERSON> ve Ahlak Bilgisi',
    'Teknoloji ve Tasarım'
  ],
  
  7: [
    '<PERSON><PERSON>rk<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    'Fen Bilimleri',
    'Sosyal Bilgiler', 
    '<PERSON>ng<PERSON><PERSON><PERSON>',
    'Görsel Sanatlar',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>ğ<PERSON>',
    '<PERSON> ve Ahlak Bilgisi',
    'Teknoloji ve Tasarım'
  ],
  
  // 8. S<PERSON>n<PERSON>f (LGS)
  8: [
    'T<PERSON>rk<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>k',
    'Fen Bilimleri',
    'T.C. İnkılap Tarihi ve Atatürkçülük',
    'Din Kültürü ve Ahlak Bilgisi',
    'İngilizce',
    'Görsel Sanatlar',
    'Müzik',
    'Beden Eğitimi',
    'Teknoloji ve Tasarım'
  ],
  
  // Lise (9-10. Sınıf)
  9: [
    'Türk Dili ve Edebiyatı',
    'Matematik',
    'Fizik',
    'Kimya',
    'Biyoloji',
    'Tarih',
    'Coğrafya',
    'Felsefe',
    'İngilizce',
    'Din Kültürü ve Ahlak Bilgisi',
    'Beden Eğitimi',
    'Görsel Sanatlar',
    'Müzik'
  ],
  
  10: [
    'Türk Dili ve Edebiyatı',
    'Matematik',
    'Fizik', 
    'Kimya',
    'Biyoloji',
    'Tarih',
    'Coğrafya',
    'Felsefe',
    'İngilizce',
    'Din Kültürü ve Ahlak Bilgisi',
    'Beden Eğitimi',
    'Görsel Sanatlar',
    'Müzik'
  ],
  
  // 11-12. Sınıf ve Mezun (YKS)
  11: [
    'Türk Dili ve Edebiyatı',
    'Matematik',
    'Fizik',
    'Kimya', 
    'Biyoloji',
    'Tarih',
    'Coğrafya',
    'Felsefe',
    'Din Kültürü ve Ahlak Bilgisi',
    'İngilizce',
    'Almanca',
    'Fransızca'
  ],
  
  12: [
    'Türk Dili ve Edebiyatı',
    'Matematik',
    'Fizik',
    'Kimya',
    'Biyoloji', 
    'Tarih',
    'Coğrafya',
    'Felsefe',
    'Din Kültürü ve Ahlak Bilgisi',
    'İngilizce',
    'Almanca',
    'Fransızca'
  ],
  
  // Mezun
  'Mezun': [
    'Türk Dili ve Edebiyatı',
    'Matematik',
    'Fizik',
    'Kimya',
    'Biyoloji',
    'Tarih', 
    'Coğrafya',
    'Felsefe',
    'Din Kültürü ve Ahlak Bilgisi',
    'İngilizce',
    'Almanca',
    'Fransızca'
  ]
};

// Get subjects for a specific class
export const getSubjectsForClass = (className) => {
  // Extract class number from class name (e.g., "5. Sınıf" -> 5)
  if (className === 'Mezun') {
    return PREDEFINED_SUBJECTS['Mezun'] || [];
  }
  
  const classNumber = parseInt(className.split('.')[0]);
  return PREDEFINED_SUBJECTS[classNumber] || [];
};

// Get all unique subjects across all classes
export const getAllSubjects = () => {
  const allSubjects = new Set();
  Object.values(PREDEFINED_SUBJECTS).forEach(subjects => {
    subjects.forEach(subject => allSubjects.add(subject));
  });
  return Array.from(allSubjects).sort();
};

// Check if a subject is valid for a specific class
export const isSubjectValidForClass = (subjectName, className) => {
  const validSubjects = getSubjectsForClass(className);
  return validSubjects.includes(subjectName);
};
