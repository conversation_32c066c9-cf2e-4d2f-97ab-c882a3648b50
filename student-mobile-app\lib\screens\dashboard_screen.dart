import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import '../providers/study_plan_provider.dart';
import '../providers/goal_provider.dart';
import '../providers/time_tracking_provider.dart';
import '../providers/user_provider.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/today_schedule_card.dart';
import '../widgets/progress_overview_card.dart';
import 'weak_topics_selection_screen.dart';
import 'mock_exams_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('StudyCoach AI'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.wait([
            context.read<StudyPlanProvider>().loadStudyPlans(),
            context.read<GoalProvider>().loadGoals(),
            context.read<TimeTrackingProvider>().loadTimeEntries(),
          ]);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(context),
              const SizedBox(height: 20),
              _buildQuickStats(context),
              const SizedBox(height: 20),
              _buildTodaySchedule(context),
              const SizedBox(height: 20),
              _buildProgressOverview(context),
              const SizedBox(height: 20),
              _buildQuickActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final greeting = userProvider.getGreeting();

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                greeting,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Bugün hedeflerine bir adım daha yaklaşmaya hazır mısın?',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Consumer3<StudyPlanProvider, GoalProvider, TimeTrackingProvider>(
      builder: (context, studyProvider, goalProvider, timeProvider, child) {
        final todayMinutes = timeProvider.getTodayTotalMinutes();
        final activeGoals = goalProvider.activeGoals.length;
        final todaySessions = studyProvider.getTodaysSessions().length;
        final completedSessions = studyProvider.getTodaysSessions()
            .where((session) => session.isCompleted).length;

        return Row(
          children: [
            Expanded(
              child: QuickStatsCard(
                title: 'Bugün',
                value: '${todayMinutes}dk',
                subtitle: 'Çalışma süresi',
                icon: Icons.timer,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickStatsCard(
                title: 'Aktif',
                value: '$activeGoals',
                subtitle: 'Hedef',
                icon: Icons.flag,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickStatsCard(
                title: 'Bugün',
                value: '$completedSessions/$todaySessions',
                subtitle: 'Tamamlanan',
                icon: Icons.check_circle,
                color: Colors.orange,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTodaySchedule(BuildContext context) {
    return Consumer<StudyPlanProvider>(
      builder: (context, provider, child) {
        final todaySessions = provider.getTodaysSessions();
        
        return TodayScheduleCard(sessions: todaySessions);
      },
    );
  }

  Widget _buildProgressOverview(BuildContext context) {
    return Consumer<GoalProvider>(
      builder: (context, provider, child) {
        final overallProgress = provider.getOverallProgress();
        
        return ProgressOverviewCard(progress: overallProgress);
      },
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Hızlı İşlemler',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'Eksik Konular',
                Icons.school,
                Colors.purple,
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WeakTopicsSelectionScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Deneme Sınavları',
                Icons.quiz,
                Colors.indigo,
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MockExamsScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Yeni Plan',
                Icons.add_circle,
                Colors.blue,
                () {
                  // TODO: Navigate to create study plan
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'Hedef Ekle',
                Icons.flag_outlined,
                Colors.green,
                () {
                  // TODO: Navigate to create goal
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Pomodoro',
                Icons.timer,
                Colors.red,
                () {
                  // TODO: Start pomodoro timer
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Container()), // Boş alan
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
