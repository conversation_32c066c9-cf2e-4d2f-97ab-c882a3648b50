import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Plus, Edit, Trash2, BookOpen, ChevronDown, ChevronRight, List, Move } from 'lucide-react';
import toast from 'react-hot-toast';
import { subjectsAPI, topicsAPI, classesAPI } from '../services/api';
import { getSubjectsForClass } from '../data/predefinedSubjects';

const SubjectsManagement = () => {
  const [selectedClassId, setSelectedClassId] = useState('');
  const [isSubjectModalOpen, setIsSubjectModalOpen] = useState(false);
  const [isTopicModalOpen, setIsTopicModalOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState(null);
  const [editingTopic, setEditingTopic] = useState(null);
  const [selectedSubjectId, setSelectedSubjectId] = useState(null);
  const [expandedSubjects, setExpandedSubjects] = useState(new Set());
  
  const [subjectFormData, setSubjectFormData] = useState({
    name: '',
    description: '',
    class_id: ''
  });

  const [topicFormData, setTopicFormData] = useState({
    name: '',
    description: '',
    subject_id: '',
    order_index: 0
  });

  const queryClient = useQueryClient();

  const { data: classes } = useQuery('classes', () => classesAPI.getAll());
  const { data: subjects, isLoading: subjectsLoading } = useQuery(
    ['subjects', selectedClassId], 
    () => subjectsAPI.getAll(selectedClassId),
    { enabled: !!selectedClassId }
  );

  const { data: topics } = useQuery(
    ['topics', selectedSubjectId],
    () => topicsAPI.getAll(selectedSubjectId),
    { enabled: !!selectedSubjectId }
  );

  // Subject mutations
  const createSubjectMutation = useMutation(subjectsAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries(['subjects', selectedClassId]);
      setIsSubjectModalOpen(false);
      resetSubjectForm();
      toast.success('Ders başarıyla oluşturuldu');
    },
    onError: () => toast.error('Ders oluşturulurken hata oluştu')
  });

  const updateSubjectMutation = useMutation(
    ({ id, data }) => subjectsAPI.update(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['subjects', selectedClassId]);
        setIsSubjectModalOpen(false);
        resetSubjectForm();
        toast.success('Ders başarıyla güncellendi');
      },
      onError: () => toast.error('Ders güncellenirken hata oluştu')
    }
  );

  const deleteSubjectMutation = useMutation(subjectsAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries(['subjects', selectedClassId]);
      toast.success('Ders başarıyla silindi');
    },
    onError: () => toast.error('Ders silinirken hata oluştu')
  });

  // Topic mutations
  const createTopicMutation = useMutation(topicsAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries(['topics', selectedSubjectId]);
      setIsTopicModalOpen(false);
      resetTopicForm();
      toast.success('Konu başarıyla oluşturuldu');
    },
    onError: () => toast.error('Konu oluşturulurken hata oluştu')
  });

  const updateTopicMutation = useMutation(
    ({ id, data }) => topicsAPI.update(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['topics', selectedSubjectId]);
        setIsTopicModalOpen(false);
        resetTopicForm();
        toast.success('Konu başarıyla güncellendi');
      },
      onError: () => toast.error('Konu güncellenirken hata oluştu')
    }
  );

  const deleteTopicMutation = useMutation(topicsAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries(['topics', selectedSubjectId]);
      toast.success('Konu başarıyla silindi');
    },
    onError: () => toast.error('Konu silinirken hata oluştu')
  });

  const resetSubjectForm = () => {
    setSubjectFormData({ name: '', description: '', class_id: selectedClassId });
    setEditingSubject(null);
  };

  const resetTopicForm = () => {
    setTopicFormData({ name: '', description: '', subject_id: selectedSubjectId, order_index: 0 });
    setEditingTopic(null);
  };

  const handleSubjectSubmit = (e) => {
    e.preventDefault();
    const formDataWithClass = { ...subjectFormData, class_id: parseInt(selectedClassId) };
    
    if (editingSubject) {
      updateSubjectMutation.mutate({ id: editingSubject.id, data: formDataWithClass });
    } else {
      createSubjectMutation.mutate(formDataWithClass);
    }
  };

  const handleTopicSubmit = (e) => {
    e.preventDefault();
    const formDataWithSubject = { ...topicFormData, subject_id: parseInt(selectedSubjectId) };
    
    if (editingTopic) {
      updateTopicMutation.mutate({ id: editingTopic.id, data: formDataWithSubject });
    } else {
      createTopicMutation.mutate(formDataWithSubject);
    }
  };

  const openSubjectModal = (subject = null) => {
    if (subject) {
      setSubjectFormData({
        name: subject.name,
        description: subject.description,
        class_id: subject.class_id
      });
      setEditingSubject(subject);
    } else {
      resetSubjectForm();
    }
    setIsSubjectModalOpen(true);
  };

  const openTopicModal = (topic = null) => {
    if (topic) {
      setTopicFormData({
        name: topic.name,
        description: topic.description,
        subject_id: topic.subject_id,
        order_index: topic.order_index
      });
      setEditingTopic(topic);
    } else {
      resetTopicForm();
    }
    setIsTopicModalOpen(true);
  };

  const toggleSubjectExpansion = (subjectId) => {
    const newExpanded = new Set(expandedSubjects);
    if (newExpanded.has(subjectId)) {
      newExpanded.delete(subjectId);
      if (selectedSubjectId === subjectId) {
        setSelectedSubjectId(null);
      }
    } else {
      newExpanded.add(subjectId);
      setSelectedSubjectId(subjectId);
    }
    setExpandedSubjects(newExpanded);
  };

  const handleDeleteSubject = (subject) => {
    if (window.confirm(`"${subject.name}" dersini silmek istediğinizden emin misiniz?`)) {
      deleteSubjectMutation.mutate(subject.id);
    }
  };

  const handleDeleteTopic = (topic) => {
    if (window.confirm(`"${topic.name}" konusunu silmek istediğinizden emin misiniz?`)) {
      deleteTopicMutation.mutate(topic.id);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Ders ve Konu Yönetimi</h1>
        <p className="mt-1 text-sm text-gray-500">
          Sınıflara göre dersleri ve konuları yönetin
        </p>
      </div>

      {/* Class Selection */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Sınıf Seçin</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sınıf
            </label>
            <select
              value={selectedClassId}
              onChange={(e) => {
                setSelectedClassId(e.target.value);
                setExpandedSubjects(new Set());
                setSelectedSubjectId(null);
              }}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Sınıf seçin...</option>
              {classes?.data?.map((cls) => (
                <option key={cls.id} value={cls.id}>
                  {cls.name}
                </option>
              ))}
            </select>
          </div>
          {selectedClassId && (
            <div className="flex items-end">
              <button
                onClick={() => openSubjectModal()}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Yeni Ders Ekle
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Subjects and Topics */}
      {selectedClassId && (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Dersler ve Konular
            </h3>
            
            {subjectsLoading ? (
              <div className="text-center py-4">Yükleniyor...</div>
            ) : subjects?.data?.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Bu sınıf için henüz ders eklenmemiş</p>
              </div>
            ) : (
              <div className="space-y-2">
                {subjects?.data?.map((subject) => (
                  <SubjectItem
                    key={subject.id}
                    subject={subject}
                    isExpanded={expandedSubjects.has(subject.id)}
                    topics={selectedSubjectId === subject.id ? topics?.data || [] : []}
                    onToggleExpansion={() => toggleSubjectExpansion(subject.id)}
                    onEditSubject={() => openSubjectModal(subject)}
                    onDeleteSubject={() => handleDeleteSubject(subject)}
                    onAddTopic={() => {
                      setSelectedSubjectId(subject.id);
                      openTopicModal();
                    }}
                    onEditTopic={openTopicModal}
                    onDeleteTopic={handleDeleteTopic}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Subject Modal */}
      {isSubjectModalOpen && (
        <SubjectModal
          isOpen={isSubjectModalOpen}
          onClose={() => setIsSubjectModalOpen(false)}
          onSubmit={handleSubjectSubmit}
          formData={subjectFormData}
          setFormData={setSubjectFormData}
          isEditing={!!editingSubject}
          isLoading={createSubjectMutation.isLoading || updateSubjectMutation.isLoading}
        />
      )}

      {/* Topic Modal */}
      {isTopicModalOpen && (
        <TopicModal
          isOpen={isTopicModalOpen}
          onClose={() => setIsTopicModalOpen(false)}
          onSubmit={handleTopicSubmit}
          formData={topicFormData}
          setFormData={setTopicFormData}
          isEditing={!!editingTopic}
          isLoading={createTopicMutation.isLoading || updateTopicMutation.isLoading}
        />
      )}
    </div>
  );
};

// SubjectItem Component
const SubjectItem = ({
  subject,
  isExpanded,
  topics,
  onToggleExpansion,
  onEditSubject,
  onDeleteSubject,
  onAddTopic,
  onEditTopic,
  onDeleteTopic
}) => {
  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="p-4 bg-gray-50 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <button
            onClick={onToggleExpansion}
            className="text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? (
              <ChevronDown className="h-5 w-5" />
            ) : (
              <ChevronRight className="h-5 w-5" />
            )}
          </button>
          <BookOpen className="h-5 w-5 text-blue-600" />
          <div>
            <h4 className="font-medium text-gray-900">{subject.name}</h4>
            {subject.description && (
              <p className="text-sm text-gray-500">{subject.description}</p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onAddTopic}
            className="text-green-600 hover:text-green-700 p-1"
            title="Konu Ekle"
          >
            <Plus className="h-4 w-4" />
          </button>
          <button
            onClick={onEditSubject}
            className="text-blue-600 hover:text-blue-700 p-1"
            title="Dersi Düzenle"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={onDeleteSubject}
            className="text-red-600 hover:text-red-700 p-1"
            title="Dersi Sil"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 border-t border-gray-200">
          {topics.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <List className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p>Bu ders için henüz konu eklenmemiş</p>
            </div>
          ) : (
            <div className="space-y-2">
              {topics.map((topic, index) => (
                <div
                  key={topic.id}
                  className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-500 font-mono">
                      {String(index + 1).padStart(2, '0')}
                    </span>
                    <div>
                      <h5 className="font-medium text-gray-900">{topic.name}</h5>
                      {topic.description && (
                        <p className="text-sm text-gray-500">{topic.description}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => onEditTopic(topic)}
                      className="text-blue-600 hover:text-blue-700 p-1"
                      title="Konuyu Düzenle"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDeleteTopic(topic)}
                      className="text-red-600 hover:text-red-700 p-1"
                      title="Konuyu Sil"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Subject Modal Component
const SubjectModal = ({ isOpen, onClose, onSubmit, formData, setFormData, isEditing, isLoading }) => {
  if (!isOpen) return null;

  const { data: classes } = useQuery('classes', () => classesAPI.getAll());
  const selectedClass = classes?.data?.find(cls => cls.id === parseInt(formData.class_id));
  const availableSubjects = selectedClass ? getSubjectsForClass(selectedClass.name) : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isEditing ? 'Dersi Düzenle' : 'Yeni Ders Ekle'}
        </h3>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sınıf *
            </label>
            <select
              value={formData.class_id}
              onChange={(e) => setFormData({ ...formData, class_id: e.target.value, name: '' })}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              required
            >
              <option value="">Sınıf seçin...</option>
              {classes?.data?.map((cls) => (
                <option key={cls.id} value={cls.id}>
                  {cls.name}
                </option>
              ))}
            </select>
          </div>

          {formData.class_id && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ders Adı *
              </label>
              <select
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                required
              >
                <option value="">Ders seçin...</option>
                {availableSubjects.map((subject) => (
                  <option key={subject} value={subject}>
                    {subject}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                {selectedClass?.name} için uygun dersler listelenmektedir
              </p>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Açıklama
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Kaydediliyor...' : (isEditing ? 'Güncelle' : 'Oluştur')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Topic Modal Component
const TopicModal = ({ isOpen, onClose, onSubmit, formData, setFormData, isEditing, isLoading }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isEditing ? 'Konuyu Düzenle' : 'Yeni Konu Ekle'}
        </h3>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Konu Adı *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Açıklama
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sıra
            </label>
            <input
              type="number"
              value={formData.order_index}
              onChange={(e) => setFormData({ ...formData, order_index: parseInt(e.target.value) || 0 })}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              min="0"
            />
          </div>
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Kaydediliyor...' : (isEditing ? 'Güncelle' : 'Oluştur')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SubjectsManagement;
