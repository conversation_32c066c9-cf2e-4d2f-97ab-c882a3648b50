import '../models/chat_message.dart';

class AIService {
  // Bu basit bir mock AI service'i. Gerçek uygulamada OpenAI, Gemini vb. API'ler kullanılabilir.
  
  Future<String> getResponse(String userMessage, List<ChatMessage> chatHistory) async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 1));
    
    final message = userMessage.toLowerCase();
    
    // Çalışma planı ile ilgili sorular
    if (message.contains('çalışma plan') || message.contains('plan oluştur')) {
      return _getStudyPlanResponse();
    }
    
    // Hedef belirleme ile ilgili sorular
    if (message.contains('hedef') || message.contains('amaç')) {
      return _getGoalResponse();
    }
    
    // Zaman yönetimi ile ilgili sorular
    if (message.contains('zaman') || message.contains('pomodoro') || message.contains('verimlilik')) {
      return _getTimeManagementResponse();
    }
    
    // Motivasyon ile ilgili sorular
    if (message.contains('motivasyon') || message.contains('isteksizlik') || message.contains('odaklan')) {
      return _getMotivationResponse();
    }
    
    // Sınav hazırlığı ile ilgili sorular
    if (message.contains('sınav') || message.contains('test') || message.contains('hazırlık')) {
      return _getExamPrepResponse();
    }
    
    // Genel öneriler
    return _getGeneralResponse();
  }
  
  String _getStudyPlanResponse() {
    final responses = [
      'Harika! Çalışma planı oluşturmak için önce hedeflerini belirleyelim. Hangi konularda çalışmak istiyorsun ve ne kadar zamanın var?',
      'Etkili bir çalışma planı için şunları öneriyorum:\n\n1. Öncelikli konuları belirle\n2. Günlük çalışma saatlerini planla\n3. Molalar için zaman ayır\n4. Haftalık değerlendirme yap\n\nHangi derslere odaklanmak istiyorsun?',
      'Çalışma planı oluştururken SMART hedefler kullanmalısın:\n- Spesifik (Belirli)\n- Measurable (Ölçülebilir)\n- Achievable (Ulaşılabilir)\n- Relevant (İlgili)\n- Time-bound (Zamana bağlı)\n\nBu hafta hangi konularda ilerleme kaydetmek istiyorsun?',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
  
  String _getGoalResponse() {
    final responses = [
      'Hedef belirleme konusunda sana yardımcı olabilirim! Kısa vadeli (1-3 ay) ve uzun vadeli (6-12 ay) hedeflerini ayırman önemli. Şu anda en çok hangi alanda gelişmek istiyorsun?',
      'Başarılı hedef belirleme için:\n\n1. Büyük hedefleri küçük adımlara böl\n2. İlerlemeyi düzenli takip et\n3. Esneklik göster\n4. Başarıları kutla\n\nBana hedeflerinden bahseder misin?',
      'Hedeflerini yazıya dökmek başarı şansını %42 artırır! Uygulamamdaki hedef takip özelliğini kullanarak ilerlemeni görselleştirebilirsin. Hangi alanda hedef koymak istiyorsun?',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
  
  String _getTimeManagementResponse() {
    final responses = [
      'Zaman yönetimi için Pomodoro Tekniği harika bir başlangıç! 25 dakika odaklanma + 5 dakika mola. Uygulamamdaki timer\'ı kullanarak deneyebilirsin. Hangi konuda başlamak istiyorsun?',
      'Etkili zaman yönetimi ipuçları:\n\n1. Günün en verimli saatlerini belirle\n2. Dikkat dağıtıcıları ortadan kaldır\n3. Benzer görevleri grupla\n4. Mola almayı unutma\n\nŞu anda en çok hangi konuda zaman sıkıntısı yaşıyorsun?',
      'Zaman takibi yaparak hangi aktivitelere ne kadar zaman harcadığını görebilirsin. Bu, verimliliğini artırmak için çok değerli bir veri. Bugün ne kadar çalışma hedefliyorsun?',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
  
  String _getMotivationResponse() {
    final responses = [
      'Motivasyon düşüklüğü normal! İşte birkaç öneri:\n\n1. Küçük başarıları kutla\n2. Çalışma ortamını değiştir\n3. Arkadaşlarınla çalışma grupları oluştur\n4. Hedeflerini hatırla\n\nBugün küçük bir adım atmaya ne dersin?',
      'Motivasyonu artırmak için:\n- Net hedefler koy\n- İlerlemeyi görselleştir\n- Kendini ödüllendir\n- Başarı hikayelerini oku\n\nSeni en çok motive eden şey nedir?',
      'Bazen mola vermek de gerekir. Kendine karşı sabırlı ol ve küçük adımlarla ilerlemeye odaklan. Her gün biraz ilerleme bile büyük fark yaratır. Bugün hangi küçük hedefe odaklanabiliriz?',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
  
  String _getExamPrepResponse() {
    final responses = [
      'Sınav hazırlığı için sistematik bir yaklaşım öneriyorum:\n\n1. Konuları listele ve önceliklendir\n2. Çalışma takvimi oluştur\n3. Aktif tekrar yöntemleri kullan\n4. Deneme sınavları çöz\n\nHangi sınava hazırlanıyorsun? (LGS, YKS, okul sınavları)',
      'Etkili sınav hazırlığı stratejileri:\n- Düzenli tekrar yap\n- Özet çıkar\n- Test çöz\n- Zor konuları tekrar et\n\nNe kadar zamanın var?',
      'Sınav stresi normal ama yönetilebilir! Düzenli çalışma, yeterli uyku ve sağlıklı beslenme çok önemli. Çalışma planını oluştururken mola zamanlarını da dahil et. Hangi konularda kendini eksik hissediyorsun?',
      'LGS için hazırlanıyorsan:\n- Temel konuları sağlam öğren\n- Düzenli test çöz\n- Zaman yönetimi pratiği yap\n\nYKS için hazırlanıyorsan:\n- Alan derslerine odaklan\n- Deneme sınavları çöz\n- Hedef üniversite belirle',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
  
  String _getGeneralResponse() {
    final responses = [
      'Sana nasıl yardımcı olabilirim? Çalışma planı, hedef belirleme, zaman yönetimi veya motivasyon konularında destek verebilirim.',
      'Başarılı öğrencilerin ortak özellikleri:\n- Düzenli çalışma alışkanlığı\n- Net hedefler\n- Etkili zaman yönetimi\n- Merak ve öğrenme isteği\n\nHangi alanda gelişmek istiyorsun?',
      'Her öğrencinin kendine özgü bir öğrenme stili var. Senin için en etkili yöntemleri birlikte keşfedelim. Şu anda en çok hangi konuda desteğe ihtiyacın var?',
      'Öğrenme bir maraton, sprint değil. Sabırlı ol ve sürekli küçük adımlar at. Ben burada sana rehberlik etmek için varım. Bugün hangi konuda başlayalım?',
      'Ortaokul ve lise yılları çok önemli! Bu dönemde kazandığın çalışma alışkanlıkları geleceğini şekillendirir. Hangi konuda sana yardımcı olabilirim?',
      'Ders çalışırken zorlandığın konular olabilir. Bu normal! Önemli olan vazgeçmemek ve doğru stratejileri kullanmak. Hangi derste zorlanıyorsun?',
    ];
    return responses[DateTime.now().millisecond % responses.length];
  }
}
