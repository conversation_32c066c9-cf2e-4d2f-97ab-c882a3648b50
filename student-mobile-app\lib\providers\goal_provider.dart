import 'package:flutter/foundation.dart';
import '../models/goal.dart';
import '../services/storage_service.dart';
import 'package:uuid/uuid.dart';

class GoalProvider with ChangeNotifier {
  final StorageService _storageService = StorageService();
  final Uuid _uuid = const Uuid();
  
  List<Goal> _goals = [];
  bool _isLoading = false;

  List<Goal> get goals => _goals;
  bool get isLoading => _isLoading;

  List<Goal> get shortTermGoals => 
      _goals.where((goal) => goal.type == GoalType.shortTerm).toList();
  
  List<Goal> get longTermGoals => 
      _goals.where((goal) => goal.type == GoalType.longTerm).toList();

  List<Goal> get activeGoals => 
      _goals.where((goal) => goal.status != GoalStatus.completed).toList();

  GoalProvider() {
    loadGoals();
  }

  Future<void> loadGoals() async {
    _isLoading = true;
    notifyListeners();

    try {
      _goals = await _storageService.getGoals();
    } catch (e) {
      debugPrint('Error loading goals: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> addGoal(Goal goal) async {
    try {
      final newGoal = Goal(
        id: _uuid.v4(),
        title: goal.title,
        description: goal.description,
        type: goal.type,
        createdDate: DateTime.now(),
        targetDate: goal.targetDate,
        category: goal.category,
      );

      _goals.add(newGoal);
      await _storageService.saveGoals(_goals);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding goal: $e');
    }
  }

  Future<void> updateGoal(Goal updatedGoal) async {
    try {
      final index = _goals.indexWhere((goal) => goal.id == updatedGoal.id);
      if (index != -1) {
        _goals[index] = updatedGoal;
        await _storageService.saveGoals(_goals);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating goal: $e');
    }
  }

  Future<void> updateGoalProgress(String goalId, int progress) async {
    try {
      final index = _goals.indexWhere((goal) => goal.id == goalId);
      if (index != -1) {
        final goal = _goals[index];
        final updatedGoal = goal.copyWith(
          progress: progress,
          status: progress >= 100 ? GoalStatus.completed : GoalStatus.inProgress,
        );
        _goals[index] = updatedGoal;
        await _storageService.saveGoals(_goals);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating goal progress: $e');
    }
  }

  Future<void> deleteGoal(String goalId) async {
    try {
      _goals.removeWhere((goal) => goal.id == goalId);
      await _storageService.saveGoals(_goals);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting goal: $e');
    }
  }

  Future<void> addMilestone(String goalId, String milestone) async {
    try {
      final index = _goals.indexWhere((goal) => goal.id == goalId);
      if (index != -1) {
        final goal = _goals[index];
        final updatedMilestones = List<String>.from(goal.milestones)
          ..add(milestone);
        final updatedGoal = goal.copyWith(milestones: updatedMilestones);
        _goals[index] = updatedGoal;
        await _storageService.saveGoals(_goals);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error adding milestone: $e');
    }
  }

  double getOverallProgress() {
    if (_goals.isEmpty) return 0.0;
    
    final totalProgress = _goals.fold<int>(0, (sum, goal) => sum + goal.progress);
    return totalProgress / (_goals.length * 100);
  }

  List<Goal> getGoalsByCategory(String category) {
    return _goals.where((goal) => goal.category == category).toList();
  }

  List<Goal> getUpcomingDeadlines() {
    final now = DateTime.now();
    final nextWeek = now.add(const Duration(days: 7));
    
    return _goals.where((goal) => 
      goal.status != GoalStatus.completed &&
      goal.targetDate.isAfter(now) &&
      goal.targetDate.isBefore(nextWeek)
    ).toList()
    ..sort((a, b) => a.targetDate.compareTo(b.targetDate));
  }
}
