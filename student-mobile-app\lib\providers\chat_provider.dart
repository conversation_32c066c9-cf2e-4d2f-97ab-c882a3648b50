import 'package:flutter/foundation.dart';
import '../models/chat_message.dart';
import '../services/storage_service.dart';
import '../services/ai_service.dart';
import 'package:uuid/uuid.dart';

class ChatProvider with ChangeNotifier {
  final StorageService _storageService = StorageService();
  final AIService _aiService = AIService();
  final Uuid _uuid = const Uuid();
  
  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isTyping = false;

  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  bool get isTyping => _isTyping;

  ChatProvider() {
    loadMessages();
    _addWelcomeMessage();
  }

  void _addWelcomeMessage() {
    if (_messages.isEmpty) {
      final welcomeMessage = ChatMessage(
        id: _uuid.v4(),
        content: 'Merhaba! Ben senin AI koçunum. Çalış<PERSON> planların, hede<PERSON>rin ve zaman yönetimin konusunda sana yardımcı olmak için buradayım. Nasıl yardımcı olabilirim?',
        type: MessageType.ai,
        timestamp: DateTime.now(),
      );
      _messages.add(welcomeMessage);
      notifyListeners();
    }
  }

  Future<void> loadMessages() async {
    _isLoading = true;
    notifyListeners();

    try {
      _messages = await _storageService.getChatMessages();
    } catch (e) {
      debugPrint('Error loading messages: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      id: _uuid.v4(),
      content: content.trim(),
      type: MessageType.user,
      timestamp: DateTime.now(),
    );

    _messages.add(userMessage);
    notifyListeners();

    // Show typing indicator
    _isTyping = true;
    notifyListeners();

    try {
      // Get AI response
      final aiResponse = await _aiService.getResponse(content, _messages);
      
      // Add AI message
      final aiMessage = ChatMessage(
        id: _uuid.v4(),
        content: aiResponse,
        type: MessageType.ai,
        timestamp: DateTime.now(),
      );

      _messages.add(aiMessage);
      
      // Save messages
      await _storageService.saveChatMessages(_messages);
    } catch (e) {
      debugPrint('Error getting AI response: $e');
      
      // Add error message
      final errorMessage = ChatMessage(
        id: _uuid.v4(),
        content: 'Üzgünüm, şu anda bir teknik sorun yaşıyorum. Lütfen daha sonra tekrar deneyin.',
        type: MessageType.ai,
        timestamp: DateTime.now(),
      );
      
      _messages.add(errorMessage);
    }

    _isTyping = false;
    notifyListeners();
  }

  Future<void> clearChat() async {
    try {
      _messages.clear();
      await _storageService.saveChatMessages(_messages);
      _addWelcomeMessage();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing chat: $e');
    }
  }

  Future<void> deleteMessage(String messageId) async {
    try {
      _messages.removeWhere((message) => message.id == messageId);
      await _storageService.saveChatMessages(_messages);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting message: $e');
    }
  }

  List<String> getSuggestedQuestions() {
    return [
      'Bu hafta için çalışma planı oluşturmama yardım eder misin?',
      'Matematik dersinde zorlanıyorum, ne yapmalıyım?',
      'LGS/YKS sınavına nasıl hazırlanmalıyım?',
      'Ders çalışırken odaklanamıyorum, önerilerini alabilir miyim?',
      'Motivasyonumu nasıl artırabilirim?',
      'Pomodoro tekniği nedir, nasıl kullanırım?',
      'Hedeflerimi nasıl belirlemeliyim?',
      'Fen bilimleri dersini daha iyi nasıl anlayabilirim?',
      'Sınav kaygımı nasıl yenebilirim?',
    ];
  }

  String getLastAIMessage() {
    final aiMessages = _messages.where((m) => m.type == MessageType.ai).toList();
    return aiMessages.isNotEmpty ? aiMessages.last.content : '';
  }
}
