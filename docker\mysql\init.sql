-- Study Tracker Database Schema

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS study_tracker;
USE study_tracker;

-- Classes table
CREATE TABLE classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Subjects table
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    class_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    INDEX idx_class_subject (class_id, name),
    UNIQUE KEY unique_class_subject (class_id, name)
);

-- Topics table for subjects
CREATE TABLE topics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    subject_id INT NOT NULL,
    order_index INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    INDEX idx_subject_topic (subject_id, order_index),
    UNIQUE KEY unique_subject_topic (subject_id, name)
);

-- Students table
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    student_number VARCHAR(20) UNIQUE,
    class_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    INDEX idx_student_number (student_number),
    INDEX idx_email (email)
);

-- Study records table
CREATE TABLE study_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    study_date DATE NOT NULL,
    duration_minutes INT NOT NULL DEFAULT 0,
    score DECIMAL(5,2) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    INDEX idx_student_subject (student_id, subject_id),
    INDEX idx_study_date (study_date)
);

-- Insert sample data
INSERT INTO classes (name, description) VALUES
('Class A', 'First year students'),
('Class B', 'Second year students'),
('Class C', 'Third year students');

INSERT INTO subjects (name, description, class_id) VALUES
-- Class A subjects
('Mathematics', 'Basic mathematics for first year', 1),
('Science', 'Introduction to science', 1),
('English', 'Basic English and grammar', 1),
-- Class B subjects
('Advanced Mathematics', 'Algebra and geometry', 2),
('Physics', 'Basic physics principles', 2),
('Chemistry', 'Introduction to chemistry', 2),
('English Literature', 'Reading and analysis', 2),
-- Class C subjects
('Calculus', 'Advanced mathematics', 3),
('Advanced Physics', 'Complex physics concepts', 3),
('Organic Chemistry', 'Advanced chemistry', 3),
('World Literature', 'Global literary works', 3),
('Computer Science', 'Programming and algorithms', 3);

INSERT INTO topics (name, description, subject_id, order_index) VALUES
-- Mathematics topics (Class A)
('Numbers and Operations', 'Basic arithmetic operations', 1, 1),
('Fractions and Decimals', 'Working with fractions and decimals', 1, 2),
('Basic Geometry', 'Shapes and measurements', 1, 3),
-- Science topics (Class A)
('Living Things', 'Introduction to biology', 2, 1),
('Matter and Materials', 'Basic chemistry concepts', 2, 2),
('Forces and Motion', 'Basic physics', 2, 3),
-- English topics (Class A)
('Grammar Basics', 'Parts of speech and sentence structure', 3, 1),
('Reading Comprehension', 'Understanding texts', 3, 2),
('Writing Skills', 'Basic writing techniques', 3, 3),
-- Advanced Mathematics topics (Class B)
('Algebraic Expressions', 'Working with variables', 4, 1),
('Linear Equations', 'Solving equations', 4, 2),
('Geometric Proofs', 'Logical reasoning in geometry', 4, 3),
-- Physics topics (Class B)
('Mechanics', 'Motion and forces', 5, 1),
('Energy and Work', 'Energy transformations', 5, 2),
('Waves and Sound', 'Wave properties', 5, 3),
-- Chemistry topics (Class B)
('Atomic Structure', 'Atoms and molecules', 6, 1),
('Chemical Reactions', 'Types of reactions', 6, 2),
('Periodic Table', 'Element properties', 6, 3),
-- Calculus topics (Class C)
('Limits', 'Introduction to limits', 9, 1),
('Derivatives', 'Rate of change', 9, 2),
('Integrals', 'Area under curves', 9, 3),
-- Advanced Physics topics (Class C)
('Quantum Mechanics', 'Quantum theory basics', 10, 1),
('Thermodynamics', 'Heat and energy', 10, 2),
('Electromagnetism', 'Electric and magnetic fields', 10, 3);

INSERT INTO students (first_name, last_name, email, student_number, class_id) VALUES
('John', 'Doe', '<EMAIL>', 'STU001', 1),
('Jane', 'Smith', '<EMAIL>', 'STU002', 1),
('Bob', 'Johnson', '<EMAIL>', 'STU003', 2),
('Alice', 'Brown', '<EMAIL>', 'STU004', 2),
('Charlie', 'Wilson', '<EMAIL>', 'STU005', 3);

INSERT INTO study_records (student_id, subject_id, study_date, duration_minutes, score, notes) VALUES
(1, 1, '2024-01-15', 120, 85.5, 'Good progress in algebra'),
(1, 2, '2024-01-16', 90, 78.0, 'Needs improvement in chemistry'),
(2, 1, '2024-01-15', 150, 92.0, 'Excellent understanding'),
(2, 3, '2024-01-17', 60, 88.5, 'Great essay writing'),
(3, 4, '2024-01-18', 75, 76.0, 'Good historical analysis');
