-- Study Tracker Database Schema

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS study_tracker;
USE study_tracker;

-- Classes table
CREATE TABLE classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Subjects table
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    class_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    INDEX idx_class_subject (class_id, name),
    UNIQUE KEY unique_class_subject (class_id, name)
);

-- Topics table for subjects
CREATE TABLE topics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    subject_id INT NOT NULL,
    order_index INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    INDEX idx_subject_topic (subject_id, order_index),
    UNIQUE KEY unique_subject_topic (subject_id, name)
);

-- Students table
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    student_number VARCHAR(20) UNIQUE,
    class_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    INDEX idx_student_number (student_number),
    INDEX idx_email (email)
);

-- Study records table
CREATE TABLE study_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    study_date DATE NOT NULL,
    duration_minutes INT NOT NULL DEFAULT 0,
    score DECIMAL(5,2) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    INDEX idx_student_subject (student_id, subject_id),
    INDEX idx_study_date (study_date)
);

-- Insert all classes from 5th grade to graduate
INSERT INTO classes (name, description) VALUES
('5. Sınıf', 'İlkokul 5. sınıf öğrencileri'),
('6. Sınıf', 'Ortaokul 6. sınıf öğrencileri'),
('7. Sınıf', 'Ortaokul 7. sınıf öğrencileri'),
('8. Sınıf', 'Ortaokul 8. sınıf öğrencileri (LGS)'),
('9. Sınıf', 'Lise 9. sınıf öğrencileri'),
('10. Sınıf', 'Lise 10. sınıf öğrencileri'),
('11. Sınıf', 'Lise 11. sınıf öğrencileri'),
('12. Sınıf', 'Lise 12. sınıf öğrencileri (YKS)'),
('Mezun', 'Üniversite sınavına hazırlanan mezun öğrenciler');

INSERT INTO subjects (name, description, class_id) VALUES
-- 5. Sınıf Dersleri
('Türkçe', '5. sınıf Türkçe dersi', 1),
('Matematik', '5. sınıf Matematik dersi', 1),
('Fen Bilimleri', '5. sınıf Fen Bilimleri dersi', 1),
('Sosyal Bilgiler', '5. sınıf Sosyal Bilgiler dersi', 1),
('İngilizce', '5. sınıf İngilizce dersi', 1),

-- 6. Sınıf Dersleri
('Türkçe', '6. sınıf Türkçe dersi', 2),
('Matematik', '6. sınıf Matematik dersi', 2),
('Fen Bilimleri', '6. sınıf Fen Bilimleri dersi', 2),
('Sosyal Bilgiler', '6. sınıf Sosyal Bilgiler dersi', 2),
('İngilizce', '6. sınıf İngilizce dersi', 2),

-- 7. Sınıf Dersleri
('Türkçe', '7. sınıf Türkçe dersi', 3),
('Matematik', '7. sınıf Matematik dersi', 3),
('Fen Bilimleri', '7. sınıf Fen Bilimleri dersi', 3),
('Sosyal Bilgiler', '7. sınıf Sosyal Bilgiler dersi', 3),
('İngilizce', '7. sınıf İngilizce dersi', 3),

-- 8. Sınıf Dersleri (LGS)
('Türkçe', '8. sınıf Türkçe dersi (LGS)', 4),
('Matematik', '8. sınıf Matematik dersi (LGS)', 4),
('Fen Bilimleri', '8. sınıf Fen Bilimleri dersi (LGS)', 4),
('T.C. İnkılap Tarihi ve Atatürkçülük', '8. sınıf İnkılap Tarihi dersi (LGS)', 4),
('Din Kültürü ve Ahlak Bilgisi', '8. sınıf Din Kültürü dersi (LGS)', 4),
('İngilizce', '8. sınıf İngilizce dersi (LGS)', 4),

-- 9. Sınıf Dersleri
('Türk Dili ve Edebiyatı', '9. sınıf Türk Dili ve Edebiyatı', 5),
('Matematik', '9. sınıf Matematik', 5),
('Fizik', '9. sınıf Fizik', 5),
('Kimya', '9. sınıf Kimya', 5),
('Biyoloji', '9. sınıf Biyoloji', 5),
('Tarih', '9. sınıf Tarih', 5),
('Coğrafya', '9. sınıf Coğrafya', 5),
('İngilizce', '9. sınıf İngilizce', 5),

-- 10. Sınıf Dersleri
('Türk Dili ve Edebiyatı', '10. sınıf Türk Dili ve Edebiyatı', 6),
('Matematik', '10. sınıf Matematik', 6),
('Fizik', '10. sınıf Fizik', 6),
('Kimya', '10. sınıf Kimya', 6),
('Biyoloji', '10. sınıf Biyoloji', 6),
('Tarih', '10. sınıf Tarih', 6),
('Coğrafya', '10. sınıf Coğrafya', 6),
('İngilizce', '10. sınıf İngilizce', 6),

-- 11. Sınıf Dersleri (YKS Hazırlık)
('Türk Dili ve Edebiyatı', '11. sınıf Türk Dili ve Edebiyatı (YKS)', 7),
('Matematik', '11. sınıf Matematik (YKS)', 7),
('Fizik', '11. sınıf Fizik (YKS)', 7),
('Kimya', '11. sınıf Kimya (YKS)', 7),
('Biyoloji', '11. sınıf Biyoloji (YKS)', 7),
('Tarih', '11. sınıf Tarih (YKS)', 7),
('Coğrafya', '11. sınıf Coğrafya (YKS)', 7),
('Felsefe', '11. sınıf Felsefe (YKS)', 7),
('İngilizce', '11. sınıf İngilizce (YKS)', 7),

-- 12. Sınıf Dersleri (YKS)
('Türk Dili ve Edebiyatı', '12. sınıf Türk Dili ve Edebiyatı (YKS)', 8),
('Matematik', '12. sınıf Matematik (YKS)', 8),
('Fizik', '12. sınıf Fizik (YKS)', 8),
('Kimya', '12. sınıf Kimya (YKS)', 8),
('Biyoloji', '12. sınıf Biyoloji (YKS)', 8),
('Tarih', '12. sınıf Tarih (YKS)', 8),
('Coğrafya', '12. sınıf Coğrafya (YKS)', 8),
('Felsefe', '12. sınıf Felsefe (YKS)', 8),
('Din Kültürü ve Ahlak Bilgisi', '12. sınıf Din Kültürü (YKS)', 8),
('İngilizce', '12. sınıf İngilizce (YKS)', 8),

-- Mezun Dersleri (YKS)
('Türk Dili ve Edebiyatı', 'Mezun Türk Dili ve Edebiyatı (YKS)', 9),
('Matematik', 'Mezun Matematik (YKS)', 9),
('Fizik', 'Mezun Fizik (YKS)', 9),
('Kimya', 'Mezun Kimya (YKS)', 9),
('Biyoloji', 'Mezun Biyoloji (YKS)', 9),
('Tarih', 'Mezun Tarih (YKS)', 9),
('Coğrafya', 'Mezun Coğrafya (YKS)', 9),
('Felsefe', 'Mezun Felsefe (YKS)', 9),
('Din Kültürü ve Ahlak Bilgisi', 'Mezun Din Kültürü (YKS)', 9),
('İngilizce', 'Mezun İngilizce (YKS)', 9);

INSERT INTO topics (name, description, subject_id, order_index) VALUES
-- 5. Sınıf Türkçe Konuları
('Okuma ve Anlama', '5. sınıf okuma becerileri', 1, 1),
('Yazım Kuralları', '5. sınıf yazım ve noktalama', 1, 2),
('Dil Bilgisi', '5. sınıf temel dil bilgisi', 1, 3),

-- 5. Sınıf Matematik Konuları
('Doğal Sayılar', '5. sınıf doğal sayılar', 2, 1),
('Kesirler', '5. sınıf kesir işlemleri', 2, 2),
('Geometri', '5. sınıf temel geometri', 2, 3),

-- 8. Sınıf LGS Matematik Konuları (subject_id: 22)
('Cebirsel İfadeler', '8. sınıf cebirsel ifadeler ve özdeşlikler', 22, 1),
('Denklemler', '8. sınıf birinci dereceden denklemler', 22, 2),
('Üçgenler', '8. sınıf üçgenler ve eşlik', 22, 3),
('Dönüşüm Geometrisi', '8. sınıf dönüşüm geometrisi', 22, 4),
('Veri Analizi', '8. sınıf istatistik ve olasılık', 22, 5),

-- 8. Sınıf LGS Türkçe Konuları (subject_id: 21)
('Sözcük Türleri', '8. sınıf sözcük türleri', 21, 1),
('Cümle Bilgisi', '8. sınıf cümle çözümlemesi', 21, 2),
('Anlam Bilgisi', '8. sınıf anlam olayları', 21, 3),
('Metin Türleri', '8. sınıf metin analizi', 21, 4),

-- 12. Sınıf YKS Matematik Konuları (subject_id: 58)
('Fonksiyonlar', '12. sınıf fonksiyon türleri ve özellikleri', 58, 1),
('Türev', '12. sınıf türev uygulamaları', 58, 2),
('İntegral', '12. sınıf integral hesabı', 58, 3),
('Analitik Geometri', '12. sınıf analitik geometri', 58, 4),
('Trigonometri', '12. sınıf trigonometrik fonksiyonlar', 58, 5),

-- 12. Sınıf YKS Fizik Konuları (subject_id: 59)
('Elektrik ve Manyetizma', '12. sınıf elektrik ve manyetik alan', 59, 1),
('Dalgalar', '12. sınıf dalga mekaniği', 59, 2),
('Modern Fizik', '12. sınıf atom fiziği ve kuantum', 59, 3),
('Optik', '12. sınıf geometrik ve fizik optik', 59, 4),

-- 12. Sınıf YKS Kimya Konuları (subject_id: 60)
('Organik Kimya', '12. sınıf organik bileşikler', 60, 1),
('Kimyasal Denge', '12. sınıf denge ve hız', 60, 2),
('Elektrokimya', '12. sınıf elektroliz ve piller', 60, 3),
('Çözeltiler', '12. sınıf çözelti kimyası', 60, 4),

-- 12. Sınıf YKS Türk Dili ve Edebiyatı Konuları (subject_id: 57)
('Cumhuriyet Dönemi Edebiyatı', '12. sınıf modern Türk edebiyatı', 57, 1),
('Dil Bilgisi', '12. sınıf sözcük türleri ve cümle bilgisi', 57, 2),
('Anlatım Teknikleri', '12. sınıf kompozisyon ve anlatım', 57, 3),
('Şiir İncelemesi', '12. sınıf şiir çözümleme teknikleri', 57, 4);

INSERT INTO students (first_name, last_name, email, student_number, class_id) VALUES
('John', 'Doe', '<EMAIL>', 'STU001', 1),
('Jane', 'Smith', '<EMAIL>', 'STU002', 1),
('Bob', 'Johnson', '<EMAIL>', 'STU003', 2),
('Alice', 'Brown', '<EMAIL>', 'STU004', 2),
('Charlie', 'Wilson', '<EMAIL>', 'STU005', 3);

INSERT INTO study_records (student_id, subject_id, study_date, duration_minutes, score, notes) VALUES
(1, 1, '2024-01-15', 120, 85.5, 'Good progress in algebra'),
(1, 2, '2024-01-16', 90, 78.0, 'Needs improvement in chemistry'),
(2, 1, '2024-01-15', 150, 92.0, 'Excellent understanding'),
(2, 3, '2024-01-17', 60, 88.5, 'Great essay writing'),
(3, 4, '2024-01-18', 75, 76.0, 'Good historical analysis');
