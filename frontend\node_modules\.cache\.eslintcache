[{"C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js": "3", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js": "4", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js": "5", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js": "7", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js": "8", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js": "9", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\SubjectsManagement.js": "11"}, {"size": 919, "mtime": 1750966112770, "results": "12", "hashOfConfig": "13"}, {"size": 1342, "mtime": 1750969242358, "results": "14", "hashOfConfig": "13"}, {"size": 4516, "mtime": 1750969255617, "results": "15", "hashOfConfig": "13"}, {"size": 11071, "mtime": 1750966283789, "results": "16", "hashOfConfig": "13"}, {"size": 11188, "mtime": 1750969433256, "results": "17", "hashOfConfig": "13"}, {"size": 5105, "mtime": 1750966211892, "results": "18", "hashOfConfig": "13"}, {"size": 11986, "mtime": 1750966477837, "results": "19", "hashOfConfig": "13"}, {"size": 8367, "mtime": 1750966344840, "results": "20", "hashOfConfig": "13"}, {"size": 12685, "mtime": 1750966439553, "results": "21", "hashOfConfig": "13"}, {"size": 2095, "mtime": 1750969100769, "results": "22", "hashOfConfig": "13"}, {"size": 19011, "mtime": 1750969218168, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lj42i1", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js", ["57", "58", "59", "60"], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\SubjectsManagement.js", ["61"], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 3, "column": 30, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 38}, {"ruleId": "62", "severity": 1, "message": "66", "line": 3, "column": 40, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 51}, {"ruleId": "62", "severity": 1, "message": "67", "line": 3, "column": 53, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 65}, {"ruleId": "62", "severity": 1, "message": "68", "line": 3, "column": 67, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 71}, {"ruleId": "62", "severity": 1, "message": "69", "line": 3, "column": 73, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 77}, "no-unused-vars", "'BookOpen' is defined but never used.", "Identifier", "unusedVar", "'ChevronDown' is defined but never used.", "'ChevronRight' is defined but never used.", "'List' is defined but never used.", "'Move' is defined but never used."]