[{"C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js": "3", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js": "4", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js": "5", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js": "7", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js": "8", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js": "9", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js": "10"}, {"size": 919, "mtime": 1750966112770, "results": "11", "hashOfConfig": "12"}, {"size": 1198, "mtime": 1750966514534, "results": "13", "hashOfConfig": "12"}, {"size": 4438, "mtime": 1750966169588, "results": "14", "hashOfConfig": "12"}, {"size": 11071, "mtime": 1750966283789, "results": "15", "hashOfConfig": "12"}, {"size": 8462, "mtime": 1750966375110, "results": "16", "hashOfConfig": "12"}, {"size": 5105, "mtime": 1750966211892, "results": "17", "hashOfConfig": "12"}, {"size": 11986, "mtime": 1750966477837, "results": "18", "hashOfConfig": "12"}, {"size": 8367, "mtime": 1750966344840, "results": "19", "hashOfConfig": "12"}, {"size": 12685, "mtime": 1750966439553, "results": "20", "hashOfConfig": "12"}, {"size": 1649, "mtime": 1750966128991, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lj42i1", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js", [], []]