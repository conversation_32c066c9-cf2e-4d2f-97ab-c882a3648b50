[{"C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js": "3", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js": "4", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js": "5", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js": "7", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js": "8", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js": "9", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\SubjectsManagement.js": "11", "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\data\\predefinedSubjects.js": "12"}, {"size": 919, "mtime": 1750966112770, "results": "13", "hashOfConfig": "14"}, {"size": 1342, "mtime": 1750969242358, "results": "15", "hashOfConfig": "14"}, {"size": 4516, "mtime": 1750969255617, "results": "16", "hashOfConfig": "14"}, {"size": 11071, "mtime": 1750966283789, "results": "17", "hashOfConfig": "14"}, {"size": 12169, "mtime": 1750969966867, "results": "18", "hashOfConfig": "14"}, {"size": 5105, "mtime": 1750966211892, "results": "19", "hashOfConfig": "14"}, {"size": 11986, "mtime": 1750966477837, "results": "20", "hashOfConfig": "14"}, {"size": 8367, "mtime": 1750966344840, "results": "21", "hashOfConfig": "14"}, {"size": 12685, "mtime": 1750966439553, "results": "22", "hashOfConfig": "14"}, {"size": 2095, "mtime": 1750969100769, "results": "23", "hashOfConfig": "14"}, {"size": 20525, "mtime": 1750969926859, "results": "24", "hashOfConfig": "14"}, {"size": 3317, "mtime": 1750969896698, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lj42i1", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Students.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Subjects.js", ["62", "63", "64", "65"], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Analytics.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\Classes.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\StudyRecords.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\pages\\SubjectsManagement.js", ["66", "67"], [], "C:\\Users\\<USER>\\source\\repository\\coach ai\\frontend\\src\\data\\predefinedSubjects.js", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 3, "column": 30, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 38}, {"ruleId": "68", "severity": 1, "message": "72", "line": 3, "column": 40, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 51}, {"ruleId": "68", "severity": 1, "message": "73", "line": 3, "column": 53, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 65}, {"ruleId": "68", "severity": 1, "message": "74", "line": 3, "column": 67, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 71}, {"ruleId": "68", "severity": 1, "message": "75", "line": 3, "column": 73, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 77}, {"ruleId": "76", "severity": 2, "message": "77", "line": 427, "column": 29, "nodeType": "70", "endLine": 427, "endColumn": 37}, "no-unused-vars", "'BookOpen' is defined but never used.", "Identifier", "unusedVar", "'ChevronDown' is defined but never used.", "'ChevronRight' is defined but never used.", "'List' is defined but never used.", "'Move' is defined but never used.", "react-hooks/rules-of-hooks", "React Hook \"useQuery\" is called conditionally. React Hooks must be called in the exact same order in every component render."]