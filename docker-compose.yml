version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: study_tracker_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: study_tracker
      MYSQL_USER: study_user
      MY<PERSON><PERSON>_PASSWORD: study_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - study_tracker_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: study_tracker_backend
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: study_user
      DB_PASSWORD: study_password
      DB_NAME: study_tracker
      PORT: 8080
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production-2024
      ACCESS_TOKEN_EXPIRY: 24h
      REFRESH_TOKEN_EXPIRY: 168h
      BCRYPT_COST: 12
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - study_tracker_network
    volumes:
      - ./backend:/app
    command: ["./wait-for-it.sh", "mysql:3306", "--", "go", "run", "main.go"]

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: study_tracker_frontend
    environment:
      REACT_APP_API_URL: http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - study_tracker_network
    volumes:
      - ./frontend:/app
      - /app/node_modules

volumes:
  mysql_data:

networks:
  study_tracker_network:
    driver: bridge
