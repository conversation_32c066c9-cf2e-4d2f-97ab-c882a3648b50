enum DayOfWeek {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday,
}

extension DayOfWeekExtension on DayOfWeek {
  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return '<PERSON><PERSON>si';
      case DayOfWeek.tuesday:
        return 'Sal<PERSON>';
      case DayOfWeek.wednesday:
        return '<PERSON><PERSON><PERSON><PERSON><PERSON>';
      case DayOfWeek.thursday:
        return 'Perşembe';
      case DayOfWeek.friday:
        return 'Cuma';
      case DayOfWeek.saturday:
        return '<PERSON>umarte<PERSON>';
      case DayOfWeek.sunday:
        return 'Pazar';
    }
  }

  String get shortName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Pzt';
      case DayOfWeek.tuesday:
        return 'Sal';
      case DayOfWeek.wednesday:
        return 'Çar';
      case DayOfWeek.thursday:
        return 'Per';
      case DayOfWeek.friday:
        return 'Cum';
      case DayOfWeek.saturday:
        return 'Cmt';
      case DayOfWeek.sunday:
        return 'Paz';
    }
  }
}

class TimeSlot {
  final int hour;
  final int minute;

  TimeSlot({required this.hour, required this.minute});

  String get displayTime {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  Map<String, dynamic> toJson() {
    return {
      'hour': hour,
      'minute': minute,
    };
  }

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      hour: json['hour'],
      minute: json['minute'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlot && other.hour == hour && other.minute == minute;
  }

  @override
  int get hashCode => hour.hashCode ^ minute.hashCode;
}

class ScheduleEntry {
  final String id;
  final DayOfWeek dayOfWeek;
  final TimeSlot startTime;
  final TimeSlot endTime;
  final String title;
  final String? description;
  final String? location;

  ScheduleEntry({
    required this.id,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.title,
    this.description,
    this.location,
  });

  int get durationMinutes {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    return endMinutes - startMinutes;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dayOfWeek': dayOfWeek.toString(),
      'startTime': startTime.toJson(),
      'endTime': endTime.toJson(),
      'title': title,
      'description': description,
      'location': location,
    };
  }

  factory ScheduleEntry.fromJson(Map<String, dynamic> json) {
    return ScheduleEntry(
      id: json['id'],
      dayOfWeek: DayOfWeek.values.firstWhere(
        (e) => e.toString() == json['dayOfWeek'],
      ),
      startTime: TimeSlot.fromJson(json['startTime']),
      endTime: TimeSlot.fromJson(json['endTime']),
      title: json['title'],
      description: json['description'],
      location: json['location'],
    );
  }

  ScheduleEntry copyWith({
    String? id,
    DayOfWeek? dayOfWeek,
    TimeSlot? startTime,
    TimeSlot? endTime,
    String? title,
    String? description,
    String? location,
  }) {
    return ScheduleEntry(
      id: id ?? this.id,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
    );
  }
}

class WeeklySchedule {
  final List<ScheduleEntry> entries;

  WeeklySchedule({this.entries = const []});

  List<ScheduleEntry> getEntriesForDay(DayOfWeek day) {
    return entries.where((entry) => entry.dayOfWeek == day).toList()
      ..sort((a, b) {
        final aMinutes = a.startTime.hour * 60 + a.startTime.minute;
        final bMinutes = b.startTime.hour * 60 + b.startTime.minute;
        return aMinutes.compareTo(bMinutes);
      });
  }

  int getTotalWeeklyMinutes() {
    return entries.fold(0, (sum, entry) => sum + entry.durationMinutes);
  }

  Map<String, dynamic> toJson() {
    return {
      'entries': entries.map((e) => e.toJson()).toList(),
    };
  }

  factory WeeklySchedule.fromJson(Map<String, dynamic> json) {
    return WeeklySchedule(
      entries: (json['entries'] as List?)
          ?.map((e) => ScheduleEntry.fromJson(e))
          .toList() ?? [],
    );
  }
}
