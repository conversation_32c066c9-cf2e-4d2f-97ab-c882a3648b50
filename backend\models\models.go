package models

import "time"

// Class represents a class/grade level
type Class struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Subject represents a subject/course
type Subject struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	ClassID     int       `json:"class_id" db:"class_id"`
	ClassName   string    `json:"class_name,omitempty" db:"class_name"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type Topic struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	SubjectID   int       `json:"subject_id" db:"subject_id"`
	SubjectName string    `json:"subject_name,omitempty" db:"subject_name"`
	OrderIndex  int       `json:"order_index" db:"order_index"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Student represents a student
type Student struct {
	ID            int       `json:"id" db:"id"`
	FirstName     string    `json:"first_name" db:"first_name"`
	LastName      string    `json:"last_name" db:"last_name"`
	Email         string    `json:"email" db:"email"`
	StudentNumber string    `json:"student_number" db:"student_number"`
	ClassID       *int      `json:"class_id" db:"class_id"`
	ClassName     string    `json:"class_name,omitempty" db:"class_name"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// StudyRecord represents a study session record
type StudyRecord struct {
	ID              int       `json:"id" db:"id"`
	StudentID       int       `json:"student_id" db:"student_id"`
	SubjectID       int       `json:"subject_id" db:"subject_id"`
	StudyDate       time.Time `json:"study_date" db:"study_date"`
	DurationMinutes int       `json:"duration_minutes" db:"duration_minutes"`
	Score           *float64  `json:"score" db:"score"`
	Notes           string    `json:"notes" db:"notes"`
	StudentName     string    `json:"student_name,omitempty" db:"student_name"`
	SubjectName     string    `json:"subject_name,omitempty" db:"subject_name"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

// CreateStudentRequest represents the request body for creating a student
type CreateStudentRequest struct {
	FirstName     string `json:"first_name" binding:"required"`
	LastName      string `json:"last_name" binding:"required"`
	Email         string `json:"email" binding:"required,email"`
	StudentNumber string `json:"student_number" binding:"required"`
	ClassID       *int   `json:"class_id"`
}

// CreateStudyRecordRequest represents the request body for creating a study record
type CreateStudyRecordRequest struct {
	StudentID       int      `json:"student_id" binding:"required"`
	SubjectID       int      `json:"subject_id" binding:"required"`
	StudyDate       string   `json:"study_date" binding:"required"`
	DurationMinutes int      `json:"duration_minutes" binding:"required,min=1"`
	Score           *float64 `json:"score"`
	Notes           string   `json:"notes"`
}

// UpdateStudyRecordRequest represents the request body for updating a study record
type UpdateStudyRecordRequest struct {
	StudentID       *int     `json:"student_id"`
	SubjectID       *int     `json:"subject_id"`
	StudyDate       *string  `json:"study_date"`
	DurationMinutes *int     `json:"duration_minutes"`
	Score           *float64 `json:"score"`
	Notes           *string  `json:"notes"`
}
