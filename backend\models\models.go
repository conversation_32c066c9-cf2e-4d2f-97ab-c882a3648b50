package models

import (
	"time"
	"github.com/golang-jwt/jwt/v5"
)

// Class represents a class/grade level
type Class struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Subject represents a subject/course
type Subject struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	ClassID     int       `json:"class_id" db:"class_id"`
	ClassName   string    `json:"class_name,omitempty" db:"class_name"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type Topic struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	SubjectID   int       `json:"subject_id" db:"subject_id"`
	SubjectName string    `json:"subject_name,omitempty" db:"subject_name"`
	OrderIndex  int       `json:"order_index" db:"order_index"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Student represents a student
type Student struct {
	ID            int       `json:"id" db:"id"`
	FirstName     string    `json:"first_name" db:"first_name"`
	LastName      string    `json:"last_name" db:"last_name"`
	Email         string    `json:"email" db:"email"`
	StudentNumber string    `json:"student_number" db:"student_number"`
	ClassID       *int      `json:"class_id" db:"class_id"`
	ClassName     string    `json:"class_name,omitempty" db:"class_name"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// StudyRecord represents a study session record
type StudyRecord struct {
	ID              int       `json:"id" db:"id"`
	StudentID       int       `json:"student_id" db:"student_id"`
	SubjectID       int       `json:"subject_id" db:"subject_id"`
	StudyDate       time.Time `json:"study_date" db:"study_date"`
	DurationMinutes int       `json:"duration_minutes" db:"duration_minutes"`
	Score           *float64  `json:"score" db:"score"`
	Notes           string    `json:"notes" db:"notes"`
	StudentName     string    `json:"student_name,omitempty" db:"student_name"`
	SubjectName     string    `json:"subject_name,omitempty" db:"subject_name"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

// CreateStudentRequest represents the request body for creating a student
type CreateStudentRequest struct {
	FirstName     string `json:"first_name" binding:"required"`
	LastName      string `json:"last_name" binding:"required"`
	Email         string `json:"email" binding:"required,email"`
	StudentNumber string `json:"student_number" binding:"required"`
	ClassID       *int   `json:"class_id"`
}

// CreateStudyRecordRequest represents the request body for creating a study record
type CreateStudyRecordRequest struct {
	StudentID       int      `json:"student_id" binding:"required"`
	SubjectID       int      `json:"subject_id" binding:"required"`
	StudyDate       string   `json:"study_date" binding:"required"`
	DurationMinutes int      `json:"duration_minutes" binding:"required,min=1"`
	Score           *float64 `json:"score"`
	Notes           string   `json:"notes"`
}

// UpdateStudyRecordRequest represents the request body for updating a study record
type UpdateStudyRecordRequest struct {
	StudentID       *int     `json:"student_id"`
	SubjectID       *int     `json:"subject_id"`
	StudyDate       *string  `json:"study_date"`
	DurationMinutes *int     `json:"duration_minutes"`
	Score           *float64 `json:"score"`
	Notes           *string  `json:"notes"`
}

// UserType represents different types of users
type UserType string

const (
	UserTypeStudent UserType = "student"
	UserTypeTeacher UserType = "teacher"
	UserTypeParent  UserType = "parent"
	UserTypeAdmin   UserType = "admin"
)

// User represents a user in the system
type User struct {
	ID                int       `json:"id" db:"id"`
	Username          string    `json:"username" db:"username"`
	Email             string    `json:"email" db:"email"`
	PasswordHash      string    `json:"-" db:"password_hash"`
	FirstName         string    `json:"first_name" db:"first_name"`
	LastName          string    `json:"last_name" db:"last_name"`
	UserType          UserType  `json:"user_type" db:"user_type"`
	IsActive          bool      `json:"is_active" db:"is_active"`
	IsEmailVerified   bool      `json:"is_email_verified" db:"is_email_verified"`
	LastLoginAt       *time.Time `json:"last_login_at" db:"last_login_at"`
	PasswordResetToken *string   `json:"-" db:"password_reset_token"`
	PasswordResetExpiry *time.Time `json:"-" db:"password_reset_expiry"`
	EmailVerificationToken *string `json:"-" db:"email_verification_token"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time `json:"updated_at" db:"updated_at"`
}

// UserProfile represents user profile information
type UserProfile struct {
	ID              int      `json:"id"`
	Username        string   `json:"username"`
	Email           string   `json:"email"`
	FirstName       string   `json:"first_name"`
	LastName        string   `json:"last_name"`
	UserType        UserType `json:"user_type"`
	IsActive        bool     `json:"is_active"`
	IsEmailVerified bool     `json:"is_email_verified"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	CreatedAt       time.Time `json:"created_at"`
}

// LoginRequest represents login request payload
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents registration request payload
type RegisterRequest struct {
	Username  string   `json:"username" binding:"required,min=3,max=50"`
	Email     string   `json:"email" binding:"required,email"`
	Password  string   `json:"password" binding:"required,min=6"`
	FirstName string   `json:"first_name" binding:"required,min=2,max=50"`
	LastName  string   `json:"last_name" binding:"required,min=2,max=50"`
	UserType  UserType `json:"user_type" binding:"required"`
}

// LoginResponse represents login response payload
type LoginResponse struct {
	User         UserProfile `json:"user"`
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	ExpiresIn    int64       `json:"expires_in"`
}

// ForgotPasswordRequest represents forgot password request payload
type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ResetPasswordRequest represents reset password request payload
type ResetPasswordRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// ChangePasswordRequest represents change password request payload
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

// RefreshTokenRequest represents refresh token request payload
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// JWTClaims represents JWT token claims
type JWTClaims struct {
	UserID   int      `json:"user_id"`
	Username string   `json:"username"`
	UserType UserType `json:"user_type"`
	jwt.RegisteredClaims
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}
