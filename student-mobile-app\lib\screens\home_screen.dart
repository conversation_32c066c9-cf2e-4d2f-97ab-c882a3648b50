import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'dashboard_screen.dart';
import 'study_plan_screen.dart';
import 'goals_screen.dart';
import 'time_tracking_screen.dart';
import 'chat_screen.dart';
import 'teacher_dashboard_screen.dart';
import '../models/user.dart';
import '../providers/user_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const StudyPlanScreen(),
    const GoalsScreen(),
    const TimeTrackingScreen(),
    const ChatScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard),
      label: '<PERSON> <PERSON><PERSON>',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.calendar_today),
      label: '<PERSON>al<PERSON>ş<PERSON> Planı',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.flag),
      label: 'Hedefler',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.clock),
      label: 'Zaman',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.robot),
      label: 'AI Koç',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.currentUser;

        // Kullanıcı türüne göre yönlendirme
        if (user?.userType == UserType.teacher) {
          return const TeacherDashboardScreen();
        } else if (user?.userType == UserType.parent) {
          // Veli dashboard'u henüz oluşturulmadı, şimdilik öğrenci dashboard'u göster
          return _buildStudentHome();
        } else {
          // Öğrenci dashboard'u
          return _buildStudentHome();
        }
      },
    );
  }

  Widget _buildStudentHome() {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: _navItems,
      ),
    );
  }
}
