package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"study-tracker-backend/database"
	"study-tracker-backend/middleware"
	"study-tracker-backend/models"
	"study-tracker-backend/utils"
)

// Register handles user registration
func Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Validate password strength
	if valid, message := utils.IsPasswordStrong(req.Password); !valid {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: message,
			Error:   "weak_password",
		})
		return
	}

	// Check if username or email already exists
	var existingCount int
	err := database.DB.QueryRow(
		"SELECT COUNT(*) FROM users WHERE username = ? OR email = ?",
		req.Username, req.Email,
	).Scan(&existingCount)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Database error",
			Error:   "database_error",
		})
		return
	}

	if existingCount > 0 {
		c.JSON(http.StatusConflict, models.APIResponse{
			Success: false,
			Message: "Username or email already exists",
			Error:   "user_exists",
		})
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to process password",
			Error:   "password_hash_error",
		})
		return
	}

	// Generate email verification token
	verificationToken, err := utils.GenerateRandomToken(32)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to generate verification token",
			Error:   "token_generation_error",
		})
		return
	}

	// Insert user into database
	result, err := database.DB.Exec(`
		INSERT INTO users (username, email, password_hash, first_name, last_name, user_type, email_verification_token)
		VALUES (?, ?, ?, ?, ?, ?, ?)`,
		req.Username, req.Email, hashedPassword, req.FirstName, req.LastName, req.UserType, verificationToken,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to create user",
			Error:   "user_creation_error",
		})
		return
	}

	userID, err := result.LastInsertId()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get user ID",
			Error:   "user_id_error",
		})
		return
	}

	// Get the created user
	user, err := getUserByID(int(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve user",
			Error:   "user_retrieval_error",
		})
		return
	}

	// Generate JWT tokens
	accessToken, refreshToken, err := utils.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to generate tokens",
			Error:   "token_generation_error",
		})
		return
	}

	// Update last login time
	updateLastLogin(user.ID)

	response := models.LoginResponse{
		User: models.UserProfile{
			ID:              user.ID,
			Username:        user.Username,
			Email:           user.Email,
			FirstName:       user.FirstName,
			LastName:        user.LastName,
			UserType:        user.UserType,
			IsActive:        user.IsActive,
			IsEmailVerified: user.IsEmailVerified,
			LastLoginAt:     user.LastLoginAt,
			CreatedAt:       user.CreatedAt,
		},
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Message: "User registered successfully",
		Data:    response,
	})
}

// Login handles user authentication
func Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Get user by username or email
	user, err := getUserByUsernameOrEmail(req.Username)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "Invalid credentials",
			Error:   "invalid_credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "Account is deactivated",
			Error:   "account_deactivated",
		})
		return
	}

	// Verify password
	if !utils.CheckPassword(req.Password, user.PasswordHash) {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "Invalid credentials",
			Error:   "invalid_credentials",
		})
		return
	}

	// Generate JWT tokens
	accessToken, refreshToken, err := utils.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to generate tokens",
			Error:   "token_generation_error",
		})
		return
	}

	// Update last login time
	updateLastLogin(user.ID)

	response := models.LoginResponse{
		User: models.UserProfile{
			ID:              user.ID,
			Username:        user.Username,
			Email:           user.Email,
			FirstName:       user.FirstName,
			LastName:        user.LastName,
			UserType:        user.UserType,
			IsActive:        user.IsActive,
			IsEmailVerified: user.IsEmailVerified,
			LastLoginAt:     user.LastLoginAt,
			CreatedAt:       user.CreatedAt,
		},
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Login successful",
		Data:    response,
	})
}

// GetProfile returns the current user's profile
func GetProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
			Error:   "not_authenticated",
		})
		return
	}

	user, err := getUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "User not found",
			Error:   "user_not_found",
		})
		return
	}

	profile := models.UserProfile{
		ID:              user.ID,
		Username:        user.Username,
		Email:           user.Email,
		FirstName:       user.FirstName,
		LastName:        user.LastName,
		UserType:        user.UserType,
		IsActive:        user.IsActive,
		IsEmailVerified: user.IsEmailVerified,
		LastLoginAt:     user.LastLoginAt,
		CreatedAt:       user.CreatedAt,
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Profile retrieved successfully",
		Data:    profile,
	})
}

// ForgotPassword handles password reset requests
func ForgotPassword(c *gin.Context) {
	var req models.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Check if user exists
	user, err := getUserByEmail(req.Email)
	if err != nil {
		// Don't reveal if email exists or not for security
		c.JSON(http.StatusOK, models.APIResponse{
			Success: true,
			Message: "If the email exists, a password reset link has been sent",
		})
		return
	}

	// Generate password reset token
	resetToken, err := utils.GenerateRandomToken(32)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to generate reset token",
			Error:   "token_generation_error",
		})
		return
	}

	// Set token expiry (1 hour from now)
	expiry := time.Now().Add(1 * time.Hour)

	// Update user with reset token
	_, err = database.DB.Exec(`
		UPDATE users
		SET password_reset_token = ?, password_reset_expiry = ?
		WHERE id = ?`,
		resetToken, expiry, user.ID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to save reset token",
			Error:   "database_error",
		})
		return
	}

	// TODO: Send email with reset token
	// For now, we'll just return success
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "If the email exists, a password reset link has been sent",
	})
}

// ResetPassword handles password reset with token
func ResetPassword(c *gin.Context) {
	var req models.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Validate password strength
	if valid, message := utils.IsPasswordStrong(req.NewPassword); !valid {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: message,
			Error:   "weak_password",
		})
		return
	}

	// Find user by reset token
	var user models.User
	err := database.DB.QueryRow(`
		SELECT id, username, email, password_hash, first_name, last_name, user_type,
			   is_active, is_email_verified, last_login_at, password_reset_expiry, created_at, updated_at
		FROM users
		WHERE password_reset_token = ? AND password_reset_expiry > NOW()`,
		req.Token,
	).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.FirstName, &user.LastName, &user.UserType, &user.IsActive,
		&user.IsEmailVerified, &user.LastLoginAt, &user.PasswordResetExpiry,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid or expired reset token",
			Error:   "invalid_token",
		})
		return
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to process password",
			Error:   "password_hash_error",
		})
		return
	}

	// Update password and clear reset token
	_, err = database.DB.Exec(`
		UPDATE users
		SET password_hash = ?, password_reset_token = NULL, password_reset_expiry = NULL
		WHERE id = ?`,
		hashedPassword, user.ID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to update password",
			Error:   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Password reset successfully",
	})
}

// ChangePassword handles password change for authenticated users
func ChangePassword(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
			Error:   "not_authenticated",
		})
		return
	}

	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Get current user
	user, err := getUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "User not found",
			Error:   "user_not_found",
		})
		return
	}

	// Verify current password
	if !utils.CheckPassword(req.CurrentPassword, user.PasswordHash) {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Current password is incorrect",
			Error:   "invalid_current_password",
		})
		return
	}

	// Validate new password strength
	if valid, message := utils.IsPasswordStrong(req.NewPassword); !valid {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: message,
			Error:   "weak_password",
		})
		return
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to process password",
			Error:   "password_hash_error",
		})
		return
	}

	// Update password
	_, err = database.DB.Exec(`
		UPDATE users SET password_hash = ? WHERE id = ?`,
		hashedPassword, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to update password",
			Error:   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Password changed successfully",
	})
}

// RefreshToken handles token refresh
func RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Validate refresh token
	claims, err := utils.ValidateJWT(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "Invalid refresh token",
			Error:   "invalid_token",
		})
		return
	}

	// Get user
	user, err := getUserByID(claims.UserID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "User not found",
			Error:   "user_not_found",
		})
		return
	}

	// Check if user is still active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "Account is deactivated",
			Error:   "account_deactivated",
		})
		return
	}

	// Generate new tokens
	accessToken, refreshToken, err := utils.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to generate tokens",
			Error:   "token_generation_error",
		})
		return
	}

	response := models.LoginResponse{
		User: models.UserProfile{
			ID:              user.ID,
			Username:        user.Username,
			Email:           user.Email,
			FirstName:       user.FirstName,
			LastName:        user.LastName,
			UserType:        user.UserType,
			IsActive:        user.IsActive,
			IsEmailVerified: user.IsEmailVerified,
			LastLoginAt:     user.LastLoginAt,
			CreatedAt:       user.CreatedAt,
		},
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Token refreshed successfully",
		Data:    response,
	})
}

// Helper functions

// getUserByID retrieves a user by ID
func getUserByID(id int) (*models.User, error) {
	var user models.User
	err := database.DB.QueryRow(`
		SELECT id, username, email, password_hash, first_name, last_name, user_type,
			   is_active, is_email_verified, last_login_at, created_at, updated_at
		FROM users WHERE id = ?`, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.FirstName, &user.LastName, &user.UserType, &user.IsActive,
		&user.IsEmailVerified, &user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// getUserByUsernameOrEmail retrieves a user by username or email
func getUserByUsernameOrEmail(usernameOrEmail string) (*models.User, error) {
	var user models.User
	err := database.DB.QueryRow(`
		SELECT id, username, email, password_hash, first_name, last_name, user_type,
			   is_active, is_email_verified, last_login_at, created_at, updated_at
		FROM users WHERE username = ? OR email = ?`, usernameOrEmail, usernameOrEmail).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.FirstName, &user.LastName, &user.UserType, &user.IsActive,
		&user.IsEmailVerified, &user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// getUserByEmail retrieves a user by email
func getUserByEmail(email string) (*models.User, error) {
	var user models.User
	err := database.DB.QueryRow(`
		SELECT id, username, email, password_hash, first_name, last_name, user_type,
			   is_active, is_email_verified, last_login_at, created_at, updated_at
		FROM users WHERE email = ?`, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.FirstName, &user.LastName, &user.UserType, &user.IsActive,
		&user.IsEmailVerified, &user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// updateLastLogin updates the user's last login time
func updateLastLogin(userID int) {
	database.DB.Exec("UPDATE users SET last_login_at = NOW() WHERE id = ?", userID)
}
