import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Classes API
export const classesAPI = {
  getAll: () => api.get('/classes'),
  getById: (id) => api.get(`/classes/${id}`),
  create: (data) => api.post('/classes', data),
  update: (id, data) => api.put(`/classes/${id}`, data),
  delete: (id) => api.delete(`/classes/${id}`),
};

// Subjects API
export const subjectsAPI = {
  getAll: (classId) => api.get('/subjects', { params: classId ? { class_id: classId } : {} }),
  getById: (id) => api.get(`/subjects/${id}`),
  create: (data) => api.post('/subjects', data),
  update: (id, data) => api.put(`/subjects/${id}`, data),
  delete: (id) => api.delete(`/subjects/${id}`),
};

export const topicsAPI = {
  getAll: (subjectId) => api.get('/topics', { params: subjectId ? { subject_id: subjectId } : {} }),
  getById: (id) => api.get(`/topics/${id}`),
  create: (data) => api.post('/topics', data),
  update: (id, data) => api.put(`/topics/${id}`, data),
  delete: (id) => api.delete(`/topics/${id}`),
  reorder: (topics) => api.put('/topics/reorder', { topics })
};

// Students API
export const studentsAPI = {
  getAll: () => api.get('/students'),
  getById: (id) => api.get(`/students/${id}`),
  create: (data) => api.post('/students', data),
  update: (id, data) => api.put(`/students/${id}`, data),
  delete: (id) => api.delete(`/students/${id}`),
};

// Study Records API
export const studyRecordsAPI = {
  getAll: () => api.get('/study-records'),
  getById: (id) => api.get(`/study-records/${id}`),
  create: (data) => api.post('/study-records', data),
  update: (id, data) => api.put(`/study-records/${id}`, data),
  delete: (id) => api.delete(`/study-records/${id}`),
};

// Analytics API
export const analyticsAPI = {
  getStudentAnalytics: (studentId) => api.get(`/analytics/student/${studentId}`),
  getClassAnalytics: (classId) => api.get(`/analytics/class/${classId}`),
};

export default api;
