{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\repository\\\\coach ai\\\\frontend\\\\src\\\\pages\\\\Subjects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { Plus, Edit, Trash2, BookOpen, ChevronDown, ChevronRight, List } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { subjectsAPI, classesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subjects = () => {\n  _s();\n  var _subjects$data;\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingSubject, setEditingSubject] = useState(null);\n  const [selectedClassId, setSelectedClassId] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    class_id: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    data: classes\n  } = useQuery('classes', () => classesAPI.getAll());\n  const {\n    data: subjects,\n    isLoading\n  } = useQuery(['subjects', selectedClassId], () => subjectsAPI.getAll(selectedClassId || null), {\n    enabled: true\n  });\n  const createMutation = useMutation(subjectsAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('subjects');\n      setIsModalOpen(false);\n      toast.success('Subject created successfully');\n    },\n    onError: () => {\n      toast.error('Failed to create subject');\n    }\n  });\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => subjectsAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('subjects');\n      setIsModalOpen(false);\n      setEditingSubject(null);\n      toast.success('Subject updated successfully');\n    },\n    onError: () => {\n      toast.error('Failed to update subject');\n    }\n  });\n  const deleteMutation = useMutation(subjectsAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('subjects');\n      toast.success('Subject deleted successfully');\n    },\n    onError: () => {\n      toast.error('Failed to delete subject');\n    }\n  });\n  const handleSubmit = e => {\n    e.preventDefault();\n    const formData = new FormData(e.target);\n    const data = {\n      name: formData.get('name'),\n      description: formData.get('description')\n    };\n    if (editingSubject) {\n      updateMutation.mutate({\n        id: editingSubject.id,\n        data\n      });\n    } else {\n      createMutation.mutate(data);\n    }\n  };\n  const handleEdit = subject => {\n    setEditingSubject(subject);\n    setIsModalOpen(true);\n  };\n  const handleDelete = id => {\n    if (window.confirm('Are you sure you want to delete this subject?')) {\n      deleteMutation.mutate(id);\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Subjects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"Manage subject information and curriculum\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setEditingSubject(null);\n          setIsModalOpen(true);\n        },\n        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), \"Add Subject\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full divide-y divide-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Created\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white divide-y divide-gray-200\",\n          children: subjects === null || subjects === void 0 ? void 0 : (_subjects$data = subjects.data) === null || _subjects$data === void 0 ? void 0 : _subjects$data.map(subject => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: subject.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: subject.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: new Date(subject.created_at).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(subject),\n                className: \"text-primary-600 hover:text-primary-900 mr-4\",\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(subject.id),\n                className: \"text-red-600 hover:text-red-900\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)]\n          }, subject.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: editingSubject ? 'Edit Subject' : 'Add New Subject'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    defaultValue: (editingSubject === null || editingSubject === void 0 ? void 0 : editingSubject.name) || '',\n                    required: true,\n                    className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"description\",\n                    defaultValue: (editingSubject === null || editingSubject === void 0 ? void 0 : editingSubject.description) || '',\n                    rows: 3,\n                    className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm\",\n                children: editingSubject ? 'Update' : 'Create'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setIsModalOpen(false);\n                  setEditingSubject(null);\n                },\n                className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(Subjects, \"l3th4hhPwp2A4Dg35pW9bBqTC6Y=\", false, function () {\n  return [useQueryClient, useQuery, useQuery, useMutation, useMutation, useMutation];\n});\n_c = Subjects;\nexport default Subjects;\nvar _c;\n$RefreshReg$(_c, \"Subjects\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "useMutation", "useQueryClient", "Plus", "Edit", "Trash2", "BookOpen", "ChevronDown", "ChevronRight", "List", "toast", "subjectsAPI", "classesAPI", "jsxDEV", "_jsxDEV", "Subjects", "_s", "_subjects$data", "isModalOpen", "setIsModalOpen", "editingSubject", "setEditingSubject", "selectedClassId", "setSelectedClassId", "formData", "setFormData", "name", "description", "class_id", "queryClient", "data", "classes", "getAll", "subjects", "isLoading", "enabled", "createMutation", "create", "onSuccess", "invalidateQueries", "success", "onError", "error", "updateMutation", "id", "update", "deleteMutation", "delete", "handleSubmit", "e", "preventDefault", "FormData", "target", "get", "mutate", "handleEdit", "subject", "handleDelete", "window", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "Date", "created_at", "toLocaleDateString", "onSubmit", "type", "defaultValue", "required", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/repository/coach ai/frontend/src/pages/Subjects.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { Plus, Edit, Trash2, BookOpen, ChevronDown, ChevronRight, List } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { subjectsAPI, classesAPI } from '../services/api';\n\nconst Subjects = () => {\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingSubject, setEditingSubject] = useState(null);\n  const [selectedClassId, setSelectedClassId] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    class_id: ''\n  });\n\n  const queryClient = useQueryClient();\n\n  const { data: classes } = useQuery('classes', () => classesAPI.getAll());\n  const { data: subjects, isLoading } = useQuery(\n    ['subjects', selectedClassId],\n    () => subjectsAPI.getAll(selectedClassId || null),\n    { enabled: true }\n  );\n\n  const createMutation = useMutation(subjectsAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('subjects');\n      setIsModalOpen(false);\n      toast.success('Subject created successfully');\n    },\n    onError: () => {\n      toast.error('Failed to create subject');\n    },\n  });\n\n  const updateMutation = useMutation(\n    ({ id, data }) => subjectsAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('subjects');\n        setIsModalOpen(false);\n        setEditingSubject(null);\n        toast.success('Subject updated successfully');\n      },\n      onError: () => {\n        toast.error('Failed to update subject');\n      },\n    }\n  );\n\n  const deleteMutation = useMutation(subjectsAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('subjects');\n      toast.success('Subject deleted successfully');\n    },\n    onError: () => {\n      toast.error('Failed to delete subject');\n    },\n  });\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    const formData = new FormData(e.target);\n    const data = {\n      name: formData.get('name'),\n      description: formData.get('description'),\n    };\n\n    if (editingSubject) {\n      updateMutation.mutate({ id: editingSubject.id, data });\n    } else {\n      createMutation.mutate(data);\n    }\n  };\n\n  const handleEdit = (subject) => {\n    setEditingSubject(subject);\n    setIsModalOpen(true);\n  };\n\n  const handleDelete = (id) => {\n    if (window.confirm('Are you sure you want to delete this subject?')) {\n      deleteMutation.mutate(id);\n    }\n  };\n\n  if (isLoading) {\n    return <div className=\"flex justify-center items-center h-64\">Loading...</div>;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Subjects</h1>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Manage subject information and curriculum\n          </p>\n        </div>\n        <button\n          onClick={() => {\n            setEditingSubject(null);\n            setIsModalOpen(true);\n          }}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Subject\n        </button>\n      </div>\n\n      {/* Subjects Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Name\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Description\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {subjects?.data?.map((subject) => (\n              <tr key={subject.id}>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {subject.name}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {subject.description}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {new Date(subject.created_at).toLocaleDateString()}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                  <button\n                    onClick={() => handleEdit(subject)}\n                    className=\"text-primary-600 hover:text-primary-900 mr-4\"\n                  >\n                    <Edit className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDelete(subject.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Modal */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n            <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n            <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n              <form onSubmit={handleSubmit}>\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                    {editingSubject ? 'Edit Subject' : 'Add New Subject'}\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        name=\"name\"\n                        defaultValue={editingSubject?.name || ''}\n                        required\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Description\n                      </label>\n                      <textarea\n                        name=\"description\"\n                        defaultValue={editingSubject?.description || ''}\n                        rows={3}\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"submit\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    {editingSubject ? 'Update' : 'Create'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setIsModalOpen(false);\n                      setEditingSubject(null);\n                    }}\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Subjects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,IAAI,QAAQ,cAAc;AAC5F,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,WAAW,EAAEC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,cAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG3B,cAAc,CAAC,CAAC;EAEpC,MAAM;IAAE4B,IAAI,EAAEC;EAAQ,CAAC,GAAG/B,QAAQ,CAAC,SAAS,EAAE,MAAMY,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC;EACxE,MAAM;IAAEF,IAAI,EAAEG,QAAQ;IAAEC;EAAU,CAAC,GAAGlC,QAAQ,CAC5C,CAAC,UAAU,EAAEsB,eAAe,CAAC,EAC7B,MAAMX,WAAW,CAACqB,MAAM,CAACV,eAAe,IAAI,IAAI,CAAC,EACjD;IAAEa,OAAO,EAAE;EAAK,CAClB,CAAC;EAED,MAAMC,cAAc,GAAGnC,WAAW,CAACU,WAAW,CAAC0B,MAAM,EAAE;IACrDC,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,UAAU,CAAC;MACzCpB,cAAc,CAAC,KAAK,CAAC;MACrBT,KAAK,CAAC8B,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACb/B,KAAK,CAACgC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG1C,WAAW,CAChC,CAAC;IAAE2C,EAAE;IAAEd;EAAK,CAAC,KAAKnB,WAAW,CAACkC,MAAM,CAACD,EAAE,EAAEd,IAAI,CAAC,EAC9C;IACEQ,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,UAAU,CAAC;MACzCpB,cAAc,CAAC,KAAK,CAAC;MACrBE,iBAAiB,CAAC,IAAI,CAAC;MACvBX,KAAK,CAAC8B,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACb/B,KAAK,CAACgC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAMI,cAAc,GAAG7C,WAAW,CAACU,WAAW,CAACoC,MAAM,EAAE;IACrDT,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,UAAU,CAAC;MACzC7B,KAAK,CAAC8B,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACb/B,KAAK,CAACgC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;EAEF,MAAMM,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAM1B,QAAQ,GAAG,IAAI2B,QAAQ,CAACF,CAAC,CAACG,MAAM,CAAC;IACvC,MAAMtB,IAAI,GAAG;MACXJ,IAAI,EAAEF,QAAQ,CAAC6B,GAAG,CAAC,MAAM,CAAC;MAC1B1B,WAAW,EAAEH,QAAQ,CAAC6B,GAAG,CAAC,aAAa;IACzC,CAAC;IAED,IAAIjC,cAAc,EAAE;MAClBuB,cAAc,CAACW,MAAM,CAAC;QAAEV,EAAE,EAAExB,cAAc,CAACwB,EAAE;QAAEd;MAAK,CAAC,CAAC;IACxD,CAAC,MAAM;MACLM,cAAc,CAACkB,MAAM,CAACxB,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC9BnC,iBAAiB,CAACmC,OAAO,CAAC;IAC1BrC,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsC,YAAY,GAAIb,EAAE,IAAK;IAC3B,IAAIc,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnEb,cAAc,CAACQ,MAAM,CAACV,EAAE,CAAC;IAC3B;EACF,CAAC;EAED,IAAIV,SAAS,EAAE;IACb,oBAAOpB,OAAA;MAAK8C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChF;EAEA,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/C,OAAA;MAAK8C,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/C,OAAA;QAAA+C,QAAA,gBACE/C,OAAA;UAAI8C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DnD,OAAA;UAAG8C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNnD,OAAA;QACEoD,OAAO,EAAEA,CAAA,KAAM;UACb7C,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAAC,IAAI,CAAC;QACtB,CAAE;QACFyC,SAAS,EAAC,sJAAsJ;QAAAC,QAAA,gBAEhK/C,OAAA,CAACX,IAAI;UAACyD,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzD/C,OAAA;QAAO8C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBACpD/C,OAAA;UAAO8C,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC3B/C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRnD,OAAA;UAAO8C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EACjD5B,QAAQ,aAARA,QAAQ,wBAAAhB,cAAA,GAARgB,QAAQ,CAAEH,IAAI,cAAAb,cAAA,uBAAdA,cAAA,CAAgBkD,GAAG,CAAEX,OAAO,iBAC3B1C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzC/C,OAAA;gBAAK8C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CL,OAAO,CAAC9B;cAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DL,OAAO,CAAC7B;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9D,IAAIO,IAAI,CAACZ,OAAO,CAACa,UAAU,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACLnD,OAAA;cAAI8C,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC7D/C,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAACC,OAAO,CAAE;gBACnCI,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,eAExD/C,OAAA,CAACV,IAAI;kBAACwD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACTnD,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMT,YAAY,CAACD,OAAO,CAACZ,EAAE,CAAE;gBACxCgB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,eAE3C/C,OAAA,CAACT,MAAM;kBAACuD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GAzBET,OAAO,CAACZ,EAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0Bf,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/C,WAAW,iBACVJ,OAAA;MAAK8C,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjD/C,OAAA;QAAK8C,SAAS,EAAC,wFAAwF;QAAAC,QAAA,gBACrG/C,OAAA;UAAK8C,SAAS,EAAC;QAA4D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EnD,OAAA;UAAK8C,SAAS,EAAC,0JAA0J;UAAAC,QAAA,eACvK/C,OAAA;YAAMyD,QAAQ,EAAEvB,YAAa;YAAAa,QAAA,gBAC3B/C,OAAA;cAAK8C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/C,OAAA;gBAAI8C,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EACnDzC,cAAc,GAAG,cAAc,GAAG;cAAiB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACLnD,OAAA;gBAAK8C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB/C,OAAA;kBAAA+C,QAAA,gBACE/C,OAAA;oBAAO8C,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRnD,OAAA;oBACE0D,IAAI,EAAC,MAAM;oBACX9C,IAAI,EAAC,MAAM;oBACX+C,YAAY,EAAE,CAAArD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEM,IAAI,KAAI,EAAG;oBACzCgD,QAAQ;oBACRd,SAAS,EAAC;kBAAwG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnD,OAAA;kBAAA+C,QAAA,gBACE/C,OAAA;oBAAO8C,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRnD,OAAA;oBACEY,IAAI,EAAC,aAAa;oBAClB+C,YAAY,EAAE,CAAArD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEO,WAAW,KAAI,EAAG;oBAChDgD,IAAI,EAAE,CAAE;oBACRf,SAAS,EAAC;kBAAwG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACvE/C,OAAA;gBACE0D,IAAI,EAAC,QAAQ;gBACbZ,SAAS,EAAC,yQAAyQ;gBAAAC,QAAA,EAElRzC,cAAc,GAAG,QAAQ,GAAG;cAAQ;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACTnD,OAAA;gBACE0D,IAAI,EAAC,QAAQ;gBACbN,OAAO,EAAEA,CAAA,KAAM;kBACb/C,cAAc,CAAC,KAAK,CAAC;kBACrBE,iBAAiB,CAAC,IAAI,CAAC;gBACzB,CAAE;gBACFuC,SAAS,EAAC,4QAA4Q;gBAAAC,QAAA,EACvR;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjD,EAAA,CA7NID,QAAQ;EAAA,QAUQb,cAAc,EAERF,QAAQ,EACIA,QAAQ,EAMvBC,WAAW,EAWXA,WAAW,EAeXA,WAAW;AAAA;AAAA2E,EAAA,GA7C9B7D,QAAQ;AA+Nd,eAAeA,QAAQ;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}