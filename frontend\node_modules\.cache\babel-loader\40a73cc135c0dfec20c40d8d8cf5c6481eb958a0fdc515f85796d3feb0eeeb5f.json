{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\repository\\\\coach ai\\\\frontend\\\\src\\\\pages\\\\Subjects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { Plus, Edit, Trash2, BookOpen, ChevronDown, ChevronRight, List } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { subjectsAPI, classesAPI } from '../services/api';\nimport { getSubjectsForClass } from '../data/predefinedSubjects';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subjects = () => {\n  _s();\n  var _classes$data, _subjects$data, _classes$data2;\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingSubject, setEditingSubject] = useState(null);\n  const [selectedClassId, setSelectedClassId] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    class_id: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    data: classes\n  } = useQuery('classes', () => classesAPI.getAll());\n  const {\n    data: subjects,\n    isLoading\n  } = useQuery(['subjects', selectedClassId], () => subjectsAPI.getAll(selectedClassId || null), {\n    enabled: true\n  });\n  const createMutation = useMutation(subjectsAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['subjects', selectedClassId]);\n      setIsModalOpen(false);\n      resetForm();\n      toast.success('Subject created successfully');\n    },\n    onError: () => {\n      toast.error('Failed to create subject');\n    }\n  });\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => subjectsAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['subjects', selectedClassId]);\n      setIsModalOpen(false);\n      resetForm();\n      toast.success('Subject updated successfully');\n    },\n    onError: () => {\n      toast.error('Failed to update subject');\n    }\n  });\n  const deleteMutation = useMutation(subjectsAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['subjects', selectedClassId]);\n      toast.success('Subject deleted successfully');\n    },\n    onError: () => {\n      toast.error('Failed to delete subject');\n    }\n  });\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      class_id: ''\n    });\n    setEditingSubject(null);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const data = {\n      ...formData,\n      class_id: parseInt(formData.class_id)\n    };\n    if (editingSubject) {\n      updateMutation.mutate({\n        id: editingSubject.id,\n        data\n      });\n    } else {\n      createMutation.mutate(data);\n    }\n  };\n  const handleEdit = subject => {\n    setFormData({\n      name: subject.name,\n      description: subject.description,\n      class_id: subject.class_id\n    });\n    setEditingSubject(subject);\n    setIsModalOpen(true);\n  };\n  const openModal = () => {\n    resetForm();\n    setIsModalOpen(true);\n  };\n  const handleDelete = id => {\n    if (window.confirm('Are you sure you want to delete this subject?')) {\n      deleteMutation.mutate(id);\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Subjects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"Manage subject information and curriculum\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: openModal,\n        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), \"Add Subject\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Filter by Class\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedClassId,\n            onChange: e => setSelectedClassId(e.target.value),\n            className: \"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Classes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), classes === null || classes === void 0 ? void 0 : (_classes$data = classes.data) === null || _classes$data === void 0 ? void 0 : _classes$data.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cls.id,\n              children: cls.name\n            }, cls.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full divide-y divide-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Class\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Created\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white divide-y divide-gray-200\",\n          children: subjects === null || subjects === void 0 ? void 0 : (_subjects$data = subjects.data) === null || _subjects$data === void 0 ? void 0 : _subjects$data.map(subject => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: subject.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: subject.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: subject.class_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: new Date(subject.created_at).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(subject),\n                className: \"text-primary-600 hover:text-primary-900 mr-4\",\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(subject.id),\n                className: \"text-red-600 hover:text-red-900\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, subject.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: editingSubject ? 'Edit Subject' : 'Add New Subject'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: formData.name,\n                    onChange: e => setFormData({\n                      ...formData,\n                      name: e.target.value\n                    }),\n                    required: true,\n                    className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: formData.description,\n                    onChange: e => setFormData({\n                      ...formData,\n                      description: e.target.value\n                    }),\n                    rows: 3,\n                    className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700\",\n                    children: \"Class *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: formData.class_id,\n                    onChange: e => setFormData({\n                      ...formData,\n                      class_id: e.target.value\n                    }),\n                    required: true,\n                    className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a class...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 25\n                    }, this), classes === null || classes === void 0 ? void 0 : (_classes$data2 = classes.data) === null || _classes$data2 === void 0 ? void 0 : _classes$data2.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cls.id,\n                      children: cls.name\n                    }, cls.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm\",\n                children: editingSubject ? 'Update' : 'Create'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setIsModalOpen(false);\n                  setEditingSubject(null);\n                },\n                className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(Subjects, \"l3th4hhPwp2A4Dg35pW9bBqTC6Y=\", false, function () {\n  return [useQueryClient, useQuery, useQuery, useMutation, useMutation, useMutation];\n});\n_c = Subjects;\nexport default Subjects;\nvar _c;\n$RefreshReg$(_c, \"Subjects\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "useMutation", "useQueryClient", "Plus", "Edit", "Trash2", "BookOpen", "ChevronDown", "ChevronRight", "List", "toast", "subjectsAPI", "classesAPI", "getSubjectsForClass", "jsxDEV", "_jsxDEV", "Subjects", "_s", "_classes$data", "_subjects$data", "_classes$data2", "isModalOpen", "setIsModalOpen", "editingSubject", "setEditingSubject", "selectedClassId", "setSelectedClassId", "formData", "setFormData", "name", "description", "class_id", "queryClient", "data", "classes", "getAll", "subjects", "isLoading", "enabled", "createMutation", "create", "onSuccess", "invalidateQueries", "resetForm", "success", "onError", "error", "updateMutation", "id", "update", "deleteMutation", "delete", "handleSubmit", "e", "preventDefault", "parseInt", "mutate", "handleEdit", "subject", "openModal", "handleDelete", "window", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "target", "map", "cls", "class_name", "Date", "created_at", "toLocaleDateString", "onSubmit", "type", "required", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/repository/coach ai/frontend/src/pages/Subjects.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { Plus, Edit, Trash2, BookOpen, ChevronDown, ChevronRight, List } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { subjectsAPI, classesAPI } from '../services/api';\nimport { getSubjectsForClass } from '../data/predefinedSubjects';\n\nconst Subjects = () => {\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingSubject, setEditingSubject] = useState(null);\n  const [selectedClassId, setSelectedClassId] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    class_id: ''\n  });\n\n  const queryClient = useQueryClient();\n\n  const { data: classes } = useQuery('classes', () => classesAPI.getAll());\n  const { data: subjects, isLoading } = useQuery(\n    ['subjects', selectedClassId],\n    () => subjectsAPI.getAll(selectedClassId || null),\n    { enabled: true }\n  );\n\n  const createMutation = useMutation(subjectsAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['subjects', selectedClassId]);\n      setIsModalOpen(false);\n      resetForm();\n      toast.success('Subject created successfully');\n    },\n    onError: () => {\n      toast.error('Failed to create subject');\n    },\n  });\n\n  const updateMutation = useMutation(\n    ({ id, data }) => subjectsAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries(['subjects', selectedClassId]);\n        setIsModalOpen(false);\n        resetForm();\n        toast.success('Subject updated successfully');\n      },\n      onError: () => {\n        toast.error('Failed to update subject');\n      },\n    }\n  );\n\n  const deleteMutation = useMutation(subjectsAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['subjects', selectedClassId]);\n      toast.success('Subject deleted successfully');\n    },\n    onError: () => {\n      toast.error('Failed to delete subject');\n    },\n  });\n\n  const resetForm = () => {\n    setFormData({ name: '', description: '', class_id: '' });\n    setEditingSubject(null);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    const data = {\n      ...formData,\n      class_id: parseInt(formData.class_id)\n    };\n\n    if (editingSubject) {\n      updateMutation.mutate({ id: editingSubject.id, data });\n    } else {\n      createMutation.mutate(data);\n    }\n  };\n\n  const handleEdit = (subject) => {\n    setFormData({\n      name: subject.name,\n      description: subject.description,\n      class_id: subject.class_id\n    });\n    setEditingSubject(subject);\n    setIsModalOpen(true);\n  };\n\n  const openModal = () => {\n    resetForm();\n    setIsModalOpen(true);\n  };\n\n  const handleDelete = (id) => {\n    if (window.confirm('Are you sure you want to delete this subject?')) {\n      deleteMutation.mutate(id);\n    }\n  };\n\n  if (isLoading) {\n    return <div className=\"flex justify-center items-center h-64\">Loading...</div>;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Subjects</h1>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Manage subject information and curriculum\n          </p>\n        </div>\n        <button\n          onClick={openModal}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Subject\n        </button>\n      </div>\n\n      {/* Class Filter */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Filter by Class\n            </label>\n            <select\n              value={selectedClassId}\n              onChange={(e) => setSelectedClassId(e.target.value)}\n              className=\"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"\">All Classes</option>\n              {classes?.data?.map((cls) => (\n                <option key={cls.id} value={cls.id}>\n                  {cls.name}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Subjects Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Name\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Description\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Class\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {subjects?.data?.map((subject) => (\n              <tr key={subject.id}>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {subject.name}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {subject.description}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {subject.class_name}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {new Date(subject.created_at).toLocaleDateString()}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                  <button\n                    onClick={() => handleEdit(subject)}\n                    className=\"text-primary-600 hover:text-primary-900 mr-4\"\n                  >\n                    <Edit className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDelete(subject.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Modal */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n            <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" />\n            <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n              <form onSubmit={handleSubmit}>\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                    {editingSubject ? 'Edit Subject' : 'Add New Subject'}\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                        required\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Description\n                      </label>\n                      <textarea\n                        value={formData.description}\n                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                        rows={3}\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">\n                        Class *\n                      </label>\n                      <select\n                        value={formData.class_id}\n                        onChange={(e) => setFormData({ ...formData, class_id: e.target.value })}\n                        required\n                        className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                      >\n                        <option value=\"\">Select a class...</option>\n                        {classes?.data?.map((cls) => (\n                          <option key={cls.id} value={cls.id}>\n                            {cls.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"submit\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    {editingSubject ? 'Update' : 'Create'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setIsModalOpen(false);\n                      setEditingSubject(null);\n                    }}\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Subjects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,IAAI,QAAQ,cAAc;AAC5F,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,WAAW,EAAEC,UAAU,QAAQ,iBAAiB;AACzD,SAASC,mBAAmB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IACvC8B,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG9B,cAAc,CAAC,CAAC;EAEpC,MAAM;IAAE+B,IAAI,EAAEC;EAAQ,CAAC,GAAGlC,QAAQ,CAAC,SAAS,EAAE,MAAMY,UAAU,CAACuB,MAAM,CAAC,CAAC,CAAC;EACxE,MAAM;IAAEF,IAAI,EAAEG,QAAQ;IAAEC;EAAU,CAAC,GAAGrC,QAAQ,CAC5C,CAAC,UAAU,EAAEyB,eAAe,CAAC,EAC7B,MAAMd,WAAW,CAACwB,MAAM,CAACV,eAAe,IAAI,IAAI,CAAC,EACjD;IAAEa,OAAO,EAAE;EAAK,CAClB,CAAC;EAED,MAAMC,cAAc,GAAGtC,WAAW,CAACU,WAAW,CAAC6B,MAAM,EAAE;IACrDC,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,CAAC,UAAU,EAAEjB,eAAe,CAAC,CAAC;MAC5DH,cAAc,CAAC,KAAK,CAAC;MACrBqB,SAAS,CAAC,CAAC;MACXjC,KAAK,CAACkC,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACbnC,KAAK,CAACoC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG9C,WAAW,CAChC,CAAC;IAAE+C,EAAE;IAAEf;EAAK,CAAC,KAAKtB,WAAW,CAACsC,MAAM,CAACD,EAAE,EAAEf,IAAI,CAAC,EAC9C;IACEQ,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,CAAC,UAAU,EAAEjB,eAAe,CAAC,CAAC;MAC5DH,cAAc,CAAC,KAAK,CAAC;MACrBqB,SAAS,CAAC,CAAC;MACXjC,KAAK,CAACkC,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACbnC,KAAK,CAACoC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAMI,cAAc,GAAGjD,WAAW,CAACU,WAAW,CAACwC,MAAM,EAAE;IACrDV,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,CAAC,UAAU,EAAEjB,eAAe,CAAC,CAAC;MAC5Df,KAAK,CAACkC,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDC,OAAO,EAAEA,CAAA,KAAM;MACbnC,KAAK,CAACoC,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;EAEF,MAAMH,SAAS,GAAGA,CAAA,KAAM;IACtBf,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxDP,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM4B,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMrB,IAAI,GAAG;MACX,GAAGN,QAAQ;MACXI,QAAQ,EAAEwB,QAAQ,CAAC5B,QAAQ,CAACI,QAAQ;IACtC,CAAC;IAED,IAAIR,cAAc,EAAE;MAClBwB,cAAc,CAACS,MAAM,CAAC;QAAER,EAAE,EAAEzB,cAAc,CAACyB,EAAE;QAAEf;MAAK,CAAC,CAAC;IACxD,CAAC,MAAM;MACLM,cAAc,CAACiB,MAAM,CAACvB,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMwB,UAAU,GAAIC,OAAO,IAAK;IAC9B9B,WAAW,CAAC;MACVC,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;MAClBC,WAAW,EAAE4B,OAAO,CAAC5B,WAAW;MAChCC,QAAQ,EAAE2B,OAAO,CAAC3B;IACpB,CAAC,CAAC;IACFP,iBAAiB,CAACkC,OAAO,CAAC;IAC1BpC,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMqC,SAAS,GAAGA,CAAA,KAAM;IACtBhB,SAAS,CAAC,CAAC;IACXrB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsC,YAAY,GAAIZ,EAAE,IAAK;IAC3B,IAAIa,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnEZ,cAAc,CAACM,MAAM,CAACR,EAAE,CAAC;IAC3B;EACF,CAAC;EAED,IAAIX,SAAS,EAAE;IACb,oBAAOtB,OAAA;MAAKgD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChF;EAEA,oBACErD,OAAA;IAAKgD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBjD,OAAA;MAAKgD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDjD,OAAA;QAAAiD,QAAA,gBACEjD,OAAA;UAAIgD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DrD,OAAA;UAAGgD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNrD,OAAA;QACEsD,OAAO,EAAEV,SAAU;QACnBI,SAAS,EAAC,sJAAsJ;QAAAC,QAAA,gBAEhKjD,OAAA,CAACZ,IAAI;UAAC4D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrD,OAAA;MAAKgD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CjD,OAAA;QAAKgD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDjD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA;YAAOgD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrD,OAAA;YACEuD,KAAK,EAAE7C,eAAgB;YACvB8C,QAAQ,EAAGlB,CAAC,IAAK3B,kBAAkB,CAAC2B,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAE;YACpDP,SAAS,EAAC,mGAAmG;YAAAC,QAAA,gBAE7GjD,OAAA;cAAQuD,KAAK,EAAC,EAAE;cAAAN,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACpClC,OAAO,aAAPA,OAAO,wBAAAhB,aAAA,GAAPgB,OAAO,CAAED,IAAI,cAAAf,aAAA,uBAAbA,aAAA,CAAeuD,GAAG,CAAEC,GAAG,iBACtB3D,OAAA;cAAqBuD,KAAK,EAAEI,GAAG,CAAC1B,EAAG;cAAAgB,QAAA,EAChCU,GAAG,CAAC7C;YAAI,GADE6C,GAAG,CAAC1B,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrD,OAAA;MAAKgD,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDjD,OAAA;QAAOgD,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBACpDjD,OAAA;UAAOgD,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC3BjD,OAAA;YAAAiD,QAAA,gBACEjD,OAAA;cAAIgD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRrD,OAAA;UAAOgD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EACjD5B,QAAQ,aAARA,QAAQ,wBAAAjB,cAAA,GAARiB,QAAQ,CAAEH,IAAI,cAAAd,cAAA,uBAAdA,cAAA,CAAgBsD,GAAG,CAAEf,OAAO,iBAC3B3C,OAAA;YAAAiD,QAAA,gBACEjD,OAAA;cAAIgD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCjD,OAAA;gBAAKgD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CN,OAAO,CAAC7B;cAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DN,OAAO,CAAC5B;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DN,OAAO,CAACiB;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9D,IAAIY,IAAI,CAAClB,OAAO,CAACmB,UAAU,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACLrD,OAAA;cAAIgD,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC7DjD,OAAA;gBACEsD,OAAO,EAAEA,CAAA,KAAMZ,UAAU,CAACC,OAAO,CAAE;gBACnCK,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,eAExDjD,OAAA,CAACX,IAAI;kBAAC2D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACTrD,OAAA;gBACEsD,OAAO,EAAEA,CAAA,KAAMT,YAAY,CAACF,OAAO,CAACV,EAAE,CAAE;gBACxCe,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,eAE3CjD,OAAA,CAACV,MAAM;kBAAC0D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GA5BEV,OAAO,CAACV,EAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bf,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/C,WAAW,iBACVN,OAAA;MAAKgD,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDjD,OAAA;QAAKgD,SAAS,EAAC,wFAAwF;QAAAC,QAAA,gBACrGjD,OAAA;UAAKgD,SAAS,EAAC;QAA4D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9ErD,OAAA;UAAKgD,SAAS,EAAC,0JAA0J;UAAAC,QAAA,eACvKjD,OAAA;YAAMgE,QAAQ,EAAE3B,YAAa;YAAAY,QAAA,gBAC3BjD,OAAA;cAAKgD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjD,OAAA;gBAAIgD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EACnDzC,cAAc,GAAG,cAAc,GAAG;cAAiB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACLrD,OAAA;gBAAKgD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBjD,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAOgD,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBACEiE,IAAI,EAAC,MAAM;oBACXV,KAAK,EAAE3C,QAAQ,CAACE,IAAK;oBACrB0C,QAAQ,EAAGlB,CAAC,IAAKzB,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEE,IAAI,EAAEwB,CAAC,CAACmB,MAAM,CAACF;oBAAM,CAAC,CAAE;oBACpEW,QAAQ;oBACRlB,SAAS,EAAC;kBAAwG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrD,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAOgD,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBACEuD,KAAK,EAAE3C,QAAQ,CAACG,WAAY;oBAC5ByC,QAAQ,EAAGlB,CAAC,IAAKzB,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEG,WAAW,EAAEuB,CAAC,CAACmB,MAAM,CAACF;oBAAM,CAAC,CAAE;oBAC3EY,IAAI,EAAE,CAAE;oBACRnB,SAAS,EAAC;kBAAwG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrD,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAOgD,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBACEuD,KAAK,EAAE3C,QAAQ,CAACI,QAAS;oBACzBwC,QAAQ,EAAGlB,CAAC,IAAKzB,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEI,QAAQ,EAAEsB,CAAC,CAACmB,MAAM,CAACF;oBAAM,CAAC,CAAE;oBACxEW,QAAQ;oBACRlB,SAAS,EAAC,wGAAwG;oBAAAC,QAAA,gBAElHjD,OAAA;sBAAQuD,KAAK,EAAC,EAAE;sBAAAN,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1ClC,OAAO,aAAPA,OAAO,wBAAAd,cAAA,GAAPc,OAAO,CAAED,IAAI,cAAAb,cAAA,uBAAbA,cAAA,CAAeqD,GAAG,CAAEC,GAAG,iBACtB3D,OAAA;sBAAqBuD,KAAK,EAAEI,GAAG,CAAC1B,EAAG;sBAAAgB,QAAA,EAChCU,GAAG,CAAC7C;oBAAI,GADE6C,GAAG,CAAC1B,EAAE;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACvEjD,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbjB,SAAS,EAAC,yQAAyQ;gBAAAC,QAAA,EAElRzC,cAAc,GAAG,QAAQ,GAAG;cAAQ;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACTrD,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbX,OAAO,EAAEA,CAAA,KAAM;kBACb/C,cAAc,CAAC,KAAK,CAAC;kBACrBE,iBAAiB,CAAC,IAAI,CAAC;gBACzB,CAAE;gBACFuC,SAAS,EAAC,4QAA4Q;gBAAAC,QAAA,EACvR;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnD,EAAA,CAxRID,QAAQ;EAAA,QAUQd,cAAc,EAERF,QAAQ,EACIA,QAAQ,EAMvBC,WAAW,EAYXA,WAAW,EAeXA,WAAW;AAAA;AAAAkF,EAAA,GA9C9BnE,QAAQ;AA0Rd,eAAeA,QAAQ;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}